// API状态检查脚本

// 百度地图API配置
const BAIDU_API_KEY = 'MqycZpQuzhz8JuMH40W3yfVImAYcJ1g9'
const BAIDU_BASE_URL = 'https://api.map.baidu.com'

// 检查API密钥状态
async function checkApiStatus() {
  console.log('🔍 检查百度地图API状态');
  console.log('='.repeat(50));
  
  console.log(`API密钥: ${BAIDU_API_KEY.substring(0, 8)}...`);
  console.log(`密钥长度: ${BAIDU_API_KEY.length}`);
  console.log(`API基础URL: ${BAIDU_BASE_URL}`);
  
  // 测试简单的API调用
  try {
    console.log('\n🌐 测试API连接...');
    
    const testUrl = `${BAIDU_BASE_URL}/geocoding/v3/?address=北京&output=json&ak=${BAIDU_API_KEY}`;
    console.log(`请求URL: ${testUrl}`);
    
    const response = await fetch(testUrl);
    console.log(`响应状态: ${response.status} ${response.statusText}`);
    
    const data = await response.json();
    console.log('API响应:', JSON.stringify(data, null, 2));
    
    if (data.status === 0) {
      console.log('✅ API连接正常');
    } else {
      console.log(`❌ API错误: ${data.message}`);
      
      // 分析错误类型
      if (data.message.includes('APP 服务被禁用')) {
        console.log('\n💡 解决方案:');
        console.log('1. 登录百度地图开放平台: https://lbsyun.baidu.com/');
        console.log('2. 进入应用管理页面');
        console.log('3. 找到对应的应用');
        console.log('4. 启用"地理编码"和"逆地理编码"服务');
        console.log('5. 检查应用的白名单设置');
      } else if (data.message.includes('ak')) {
        console.log('\n💡 解决方案:');
        console.log('1. 检查API密钥是否正确');
        console.log('2. 确认应用类型为"浏览器端"');
        console.log('3. 检查应用的域名白名单设置');
      }
    }
    
  } catch (error) {
    console.log(`❌ 网络错误: ${error.message}`);
    console.log('\n💡 可能的原因:');
    console.log('1. 网络连接问题');
    console.log('2. 防火墙阻止');
    console.log('3. DNS解析问题');
  }
}

// 检查环境变量
function checkEnvironment() {
  console.log('\n🔧 检查环境配置');
  console.log('='.repeat(50));
  
  console.log('Node.js版本:', process.version);
  console.log('平台:', process.platform);
  console.log('架构:', process.arch);
  
  // 检查是否有fetch支持
  if (typeof fetch !== 'undefined') {
    console.log('✅ fetch API 可用');
  } else {
    console.log('❌ fetch API 不可用');
  }
}

// 提供配置建议
function provideSuggestions() {
  console.log('\n💡 配置建议');
  console.log('='.repeat(50));
  
  console.log('1. 百度地图开放平台配置:');
  console.log('   - 访问: https://lbsyun.baidu.com/');
  console.log('   - 登录并进入应用管理');
  console.log('   - 确保应用类型为"浏览器端"');
  console.log('   - 启用"地理编码"和"逆地理编码"服务');
  console.log('   - 添加域名白名单: localhost, 127.0.0.1');
  
  console.log('\n2. 本地开发配置:');
  console.log('   - 确保网络连接正常');
  console.log('   - 检查防火墙设置');
  console.log('   - 使用HTTPS或localhost访问');
  
  console.log('\n3. 备用方案:');
  console.log('   - 系统已配置智能降级机制');
  console.log('   - API失败时自动使用模拟数据');
  console.log('   - 确保基本功能可用');
}

// 主函数
async function main() {
  console.log('🚀 百度地图API状态检查');
  console.log('='.repeat(60));
  
  try {
    checkEnvironment();
    await checkApiStatus();
    provideSuggestions();
    
    console.log('\n✅ 检查完成！');
    console.log('='.repeat(60));
    
  } catch (error) {
    console.error('❌ 检查过程中发生错误:', error);
  }
}

// 运行检查
main(); 