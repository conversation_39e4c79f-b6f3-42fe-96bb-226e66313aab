<template>
  <div class="test-map">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <h2>地图功能测试</h2>
          <el-tag :type="mapService.hasValidApiKey() ? 'success' : 'warning'">
            {{ mapService.hasValidApiKey() ? '高德地图API模式' : '模拟模式' }}
          </el-tag>
        </div>
      </template>
      
      <div class="test-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="test-section">
              <h3>客户表单地图定位测试</h3>
              <el-form :model="testForm" label-width="100px">
                <el-form-item label="省份">
                  <el-select v-model="testForm.province" placeholder="请选择省份">
                    <el-option label="北京市" value="北京市" />
                    <el-option label="上海市" value="上海市" />
                    <el-option label="广东省" value="广东省" />
                  </el-select>
                </el-form-item>
                <el-form-item label="详细地址">
                  <el-input v-model="testForm.detail" placeholder="请输入详细地址" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="showMapDialog = true">
                    地图定位
                  </el-button>
                </el-form-item>
              </el-form>
              
              <div v-if="selectedLocation" class="result-display">
                <h4>定位结果：</h4>
                <p><strong>地址：</strong>{{ selectedLocation.address }}</p>
                <p><strong>经度：</strong>{{ selectedLocation.lng.toFixed(6) }}</p>
                <p><strong>纬度：</strong>{{ selectedLocation.lat.toFixed(6) }}</p>
              </div>
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="test-section">
              <h3>地图组件测试</h3>
              <el-button type="primary" @click="showMapComponent = true">
                打开地图组件
              </el-button>
              
              <div v-if="mapComponentLocation" class="result-display">
                <h4>地图选择结果：</h4>
                <p><strong>地址：</strong>{{ mapComponentLocation.address }}</p>
                <p><strong>经度：</strong>{{ mapComponentLocation.lng.toFixed(6) }}</p>
                <p><strong>纬度：</strong>{{ mapComponentLocation.lat.toFixed(6) }}</p>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>
    
    <!-- 地图定位对话框 -->
    <MapLocationDialog
      v-model="showMapDialog"
      :initial-address="testForm.detail"
      :initial-province="testForm.province"
      @location-selected="handleLocationSelected"
    />
    
    <!-- 地图组件对话框 -->
    <el-dialog
      v-model="showMapComponent"
      title="地图组件测试"
      width="80%"
      top="5vh"
    >
      <BaiduMap
        :initial-location="mapComponentLocation"
        @location-selected="handleMapComponentLocationSelected"
        @location-cleared="handleMapComponentLocationCleared"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { mapService } from '@/services/mapService'
import MapLocationDialog from '@/components/MapLocationDialog.vue'
import BaiduMap from '@/components/BaiduMap.vue'

// 测试表单
const testForm = reactive({
  province: '北京市',
  detail: '朝阳区'
})

// 状态变量
const showMapDialog = ref(false)
const showMapComponent = ref(false)
const selectedLocation = ref<{ lng: number; lat: number; address: string } | null>(null)
const mapComponentLocation = ref<{ lng: number; lat: number; address: string } | null>(null)

// 处理地图定位对话框的位置选择
const handleLocationSelected = (location: { lng: number; lat: number; address: string }) => {
  selectedLocation.value = location
  ElMessage.success('地图定位成功')
}

// 处理地图组件的位置选择
const handleMapComponentLocationSelected = (location: { lng: number; lat: number; address: string }) => {
  mapComponentLocation.value = location
  showMapComponent.value = false
  ElMessage.success('地图选择成功')
}

// 处理地图组件的位置清除
const handleMapComponentLocationCleared = () => {
  mapComponentLocation.value = null
  ElMessage.info('地图选择已清除')
}
</script>

<style scoped>
.test-map {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #303133;
}

.test-content {
  padding: 20px 0;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h3 {
  color: #409EFF;
  margin-bottom: 15px;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 8px;
}

.result-display {
  margin-top: 20px;
  padding: 15px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #b3d8ff;
}

.result-display h4 {
  margin: 0 0 10px 0;
  color: #409EFF;
}

.result-display p {
  margin: 5px 0;
  color: #606266;
}

.result-display strong {
  color: #303133;
}
</style> 