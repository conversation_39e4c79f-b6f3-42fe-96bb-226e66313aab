<template>
  <div class="customer-manage">
    <div class="header">
      <div class="header-left">
        <h2>客户管理</h2>
        <p class="header-desc">管理客户信息，包括客户档案、级别分类、关联信息</p>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :model="searchForm" inline>
        <el-form-item label="客户编号">
          <el-input v-model="searchForm.customerNumber" placeholder="请输入客户编号" clearable />
        </el-form-item>
        <el-form-item label="客户名称">
          <el-input v-model="searchForm.customerName" placeholder="请输入客户名称" clearable />
        </el-form-item>
        <el-form-item label="客户类型">
          <el-select v-model="searchForm.customerType" placeholder="请选择" clearable style="width: 100px;">
            <el-option label="个体" value="个体" />
            <el-option label="企业" value="企业" />
          </el-select>
        </el-form-item>
        <el-form-item label="客户级别">
          <el-select v-model="searchForm.customerLevel" placeholder="请选择" clearable style="width: 100px;">
            <el-option label="普通客户" value="普通客户" />
            <el-option label="VIP客户" value="VIP客户" />
            <el-option label="重要客户" value="重要客户" />
          </el-select>
        </el-form-item>
        <el-form-item label="使用状态">
          <el-select v-model="searchForm.usageStatus" placeholder="请选择" clearable style="width: 100px;">
            <el-option label="启用" value="启用" />
            <el-option label="停用" value="停用" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="showAddDialog = true">
            <el-icon>
              <Plus />
            </el-icon>
            新增客户
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-area">
      <el-table :data="customerList" v-loading="loading" stripe>
        <el-table-column prop="customerNumber" label="客户编号" width="120" />
        <el-table-column prop="customerName" label="客户名称" width="200" />
        <el-table-column prop="customerType" label="客户类型" width="100" />
        <el-table-column prop="personInChargeName" label="负责人" width="100" />
        <el-table-column prop="personInChargePhone" label="联系电话" width="130" />
        <el-table-column prop="location" label="所在地" width="150" />
        <el-table-column prop="customerLevel" label="客户级别" width="100">
          <template #default="{ row }">
            <el-tag :type="getCustomerLevelType(row.customerLevel)">
              {{ row.customerLevel || '-' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="usageStatus" label="使用状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.usageStatus === '启用' ? 'success' : 'danger'">
              {{ row.usageStatus }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination v-model:current-page="pagination.pageIndex" v-model:page-size="pagination.pageSize"
          :page-sizes="[4, 8, 12, 16]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 添加客户对话框 -->
    <el-dialog v-model="showAddDialog" title="客户-新增" width="80%" :before-close="handleCloseAdd" class="customer-dialog">
      <CustomerForm ref="customerFormRef" @submit-success="handleAddSuccess" @cancel="handleCancel" />
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="客户详情" width="800px">
      <CustomerDetail v-if="showDetailDialog" :customer-id="selectedCustomerId" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import CustomerForm from './components/CustomerForm.vue'
import CustomerDetail from './components/CustomerDetail.vue'
import { customerApi, type Customer } from '@/services/api'

// 响应式数据
const loading = ref(false)
const customerList = ref<Customer[]>([])
const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const selectedCustomerId = ref(0)
const customerFormRef = ref()

// 搜索表单
const searchForm = reactive({
  customerNumber: '',
  customerName: '',
  customerType: '',
  customerLevel: '',
  usageStatus: ''
})

// 分页信息
const pagination = reactive({
  pageIndex: 1,
  pageSize: 4,
  total: 0
})

// 获取客户列表
const getCustomerList = async () => {
  loading.value = true
  try {
    // 构建查询参数，只包含有值的字段
    const params: any = {
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize
    }
    
    // 只添加非空的搜索条件
    if (searchForm.customerNumber && searchForm.customerNumber.trim()) {
      params.customerNumber = searchForm.customerNumber.trim()
    }
    if (searchForm.customerName && searchForm.customerName.trim()) {
      params.customerName = searchForm.customerName.trim()
    }
    if (searchForm.customerType && searchForm.customerType.trim()) {
      params.customerType = searchForm.customerType.trim()
    }
    if (searchForm.customerLevel && searchForm.customerLevel.trim()) {
      params.customerLevel = searchForm.customerLevel.trim()
    }
    if (searchForm.usageStatus && searchForm.usageStatus.trim()) {
      params.usageStatus = searchForm.usageStatus.trim()
    }
    
    const result = await customerApi.getList(params)

    if (result.code === 200) {
      customerList.value = result.data.pageData
      pagination.total = result.data.totalCount
    } else {
      ElMessage.error(result.message || '获取客户列表失败')
    }
  } catch (error) {
    console.error('查询错误:', error)
    ElMessage.error('网络错误，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageIndex = 1
  getCustomerList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    customerNumber: '',
    customerName: '',
    customerType: '',
    customerLevel: '',
    usageStatus: ''
  })
  pagination.pageIndex = 1
  getCustomerList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.pageIndex = 1
  getCustomerList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.pageIndex = page
  getCustomerList()
}

// 查看详情
const handleViewDetail = (row: any) => {
  selectedCustomerId.value = row.id
  showDetailDialog.value = true
}

// 编辑
const handleEdit = (row: any) => {
  // 实现编辑功能
  ElMessage.info('编辑功能待实现')
}

// 添加成功回调
const handleAddSuccess = () => {
  showAddDialog.value = false
  getCustomerList()
  ElMessage.success('客户添加成功')
}

// 关闭添加对话框
const handleCloseAdd = (done: () => void) => {
  if (customerFormRef.value) {
    customerFormRef.value.handleReset()
  }
  done()
}

// 处理取消事件
const handleCancel = () => {
  showAddDialog.value = false
}

// 获取客户级别标签类型
const getCustomerLevelType = (level: string) => {
  switch (level) {
    case 'VIP客户':
      return 'danger'
    case '重要客户':
      return 'warning'
    case '普通客户':
      return 'info'
    default:
      return 'info'
  }
}

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleString()
}

// 组件挂载时获取数据
onMounted(() => {
  getCustomerList()
})
</script>

<style scoped>
.customer-manage {
  height: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #67C23A 0%, #85ce61 100%);
  border-radius: 12px;
  color: white;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.header-desc {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.search-area {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.table-area {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-table) {
  margin-bottom: 20px;
}

.drawer-content {
  padding: 20px;
  height: calc(100vh - 120px);
  overflow-y: auto;
}
</style>
