<template>
  <div class="map-test-container">
    <h1>地图功能测试</h1>
    
    <!-- API状态显示 -->
    <div class="api-status">
      <h3>API状态</h3>
      <p><strong>API密钥:</strong> {{ apiKey }}</p>
      <p><strong>API状态:</strong> 
        <span :class="apiStatus ? 'status-ok' : 'status-error'">
          {{ apiStatus ? '正常' : '异常' }}
        </span>
      </p>
    </div>
    
    <!-- 搜索测试 -->
    <div class="search-test">
      <h3>地址搜索测试</h3>
      <div class="search-input">
        <el-input
          v-model="searchKeyword"
          placeholder="输入地址进行搜索（如：北京、上海、广州等）"
          @keyup.enter="testSearch"
        >
          <template #append>
            <el-button @click="testSearch" :loading="searching">
              搜索
            </el-button>
          </template>
        </el-input>
      </div>
      
      <!-- 搜索结果 -->
      <div v-if="searchResult" class="search-result">
        <h4>搜索结果:</h4>
        <div class="result-card">
          <p><strong>地址:</strong> {{ searchResult.address }}</p>
          <p><strong>经度:</strong> {{ searchResult.lng.toFixed(6) }}</p>
          <p><strong>纬度:</strong> {{ searchResult.lat.toFixed(6) }}</p>
        </div>
      </div>
      
      <!-- 错误信息 -->
      <div v-if="error" class="error-message">
        <el-alert :title="error" type="error" show-icon />
      </div>
    </div>
    
    <!-- 地图组件 -->
    <div class="map-section">
      <h3>地图显示</h3>
      <BaiduMap
        :api-key="apiKey"
        :initial-location="searchResult"
        @location-selected="handleLocationSelected"
        @location-cleared="handleLocationCleared"
      />
    </div>
    
    <!-- 控制台日志 -->
    <div class="console-log">
      <h3>调试信息</h3>
      <div class="log-container">
        <div v-for="(log, index) in logs" :key="index" class="log-item">
          <span class="log-time">{{ log.time }}</span>
          <span :class="'log-level-' + log.level">{{ log.message }}</span>
        </div>
      </div>
      <el-button @click="clearLogs" size="small">清除日志</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import BaiduMap from '@/components/BaiduMap.vue'
import { mapService, hasValidApiKey } from '@/services/mapService'

// 响应式数据
const apiKey = ref('')
const apiStatus = ref(false)
const searchKeyword = ref('')
const searching = ref(false)
const searchResult = ref<{ lng: number; lat: number; address: string } | null>(null)
const error = ref('')
const logs = ref<Array<{ time: string; level: string; message: string }>>([])

// 添加日志
const addLog = (level: string, message: string) => {
  const time = new Date().toLocaleTimeString()
  logs.value.push({ time, level, message })
  // 保持最多50条日志
  if (logs.value.length > 50) {
    logs.value.shift()
  }
}

// 清除日志
const clearLogs = () => {
  logs.value = []
}

// 测试搜索
const testSearch = async () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  
  searching.value = true
  error.value = ''
  addLog('info', `开始搜索: ${searchKeyword.value}`)
  
  try {
    const result = await mapService.searchAddress(searchKeyword.value)
    
    if (result) {
      searchResult.value = result
      addLog('success', `搜索成功: ${result.address}`)
      ElMessage.success(`搜索成功: ${result.address}`)
    } else {
      error.value = '未找到该地址'
      addLog('error', '搜索失败: 未找到地址')
      ElMessage.error('未找到该地址')
    }
  } catch (err) {
    error.value = '搜索出错'
    addLog('error', `搜索异常: ${err}`)
    ElMessage.error('搜索出错')
  } finally {
    searching.value = false
  }
}

// 处理位置选择
const handleLocationSelected = (location: { lng: number; lat: number; address: string }) => {
  addLog('info', `位置已选择: ${location.address}`)
  searchResult.value = location
}

// 处理位置清除
const handleLocationCleared = () => {
  addLog('info', '位置已清除')
  searchResult.value = null
}

// 检查API状态
const checkApiStatus = () => {
  apiKey.value = import.meta.env.VITE_BAIDU_API_KEY || 'MqycZpQuzhz8JuMH40W3yfVImAYcJ1g9'
  apiStatus.value = hasValidApiKey()
  addLog('info', `API密钥: ${apiKey.value}`)
  addLog('info', `API状态: ${apiStatus.value ? '正常' : '异常'}`)
}

// 拦截console方法
const originalConsole = {
  log: console.log,
  warn: console.warn,
  error: console.error
}

// 重写console方法
const setupConsoleIntercept = () => {
  console.log = (...args) => {
    originalConsole.log(...args)
    addLog('info', args.join(' '))
  }
  
  console.warn = (...args) => {
    originalConsole.warn(...args)
    addLog('warn', args.join(' '))
  }
  
  console.error = (...args) => {
    originalConsole.error(...args)
    addLog('error', args.join(' '))
  }
}

// 恢复console方法
const restoreConsole = () => {
  console.log = originalConsole.log
  console.warn = originalConsole.warn
  console.error = originalConsole.error
}

// 组件挂载
onMounted(() => {
  setupConsoleIntercept()
  checkApiStatus()
  addLog('info', '地图测试页面已加载')
})

// 组件卸载
onUnmounted(() => {
  restoreConsole()
})
</script>

<style scoped>
.map-test-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.api-status {
  background: #f5f7fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.status-ok {
  color: #67c23a;
  font-weight: bold;
}

.status-error {
  color: #f56c6c;
  font-weight: bold;
}

.search-test {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.search-input {
  margin-bottom: 15px;
}

.search-result {
  margin-top: 15px;
}

.result-card {
  background: #f0f9ff;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #b3d8ff;
}

.result-card p {
  margin: 5px 0;
}

.error-message {
  margin-top: 15px;
}

.map-section {
  background: #fff;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.console-log {
  background: #1e1e1e;
  color: #fff;
  padding: 20px;
  border-radius: 8px;
  font-family: 'Courier New', monospace;
}

.log-container {
  max-height: 300px;
  overflow-y: auto;
  margin-bottom: 15px;
  background: #2d2d2d;
  padding: 10px;
  border-radius: 4px;
}

.log-item {
  margin: 2px 0;
  font-size: 12px;
}

.log-time {
  color: #888;
  margin-right: 10px;
}

.log-level-info {
  color: #4fc08d;
}

.log-level-warn {
  color: #e6a23c;
}

.log-level-error {
  color: #f56c6c;
}

.log-level-success {
  color: #67c23a;
}

h1 {
  color: #303133;
  margin-bottom: 30px;
}

h3 {
  color: #606266;
  margin-bottom: 15px;
}

h4 {
  color: #606266;
  margin-bottom: 10px;
}
</style> 