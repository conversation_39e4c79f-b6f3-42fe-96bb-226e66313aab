<template>
  <div class="customer-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="140px"
      @submit.prevent="handleSubmit"
    >
      <!-- 客户编号和客户类型 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="客户编号" prop="customerNumber" required>
            <div class="customer-number-input">
              <el-input v-model="formData.customerNumber" placeholder="请输入客户编号" />
              <el-button type="primary" @click="generateCustomerNumber" style="margin-left: 8px;">
                自动生成
              </el-button>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户类型" prop="customerType" required>
            <el-radio-group v-model="formData.customerType">
              <el-radio label="个人">个人</el-radio>
              <el-radio label="企业">企业</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 负责人信息 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="负责人手机号" prop="personInChargePhone" required>
            <el-input v-model="formData.personInChargePhone" placeholder="请输入负责人手机号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人姓名" prop="personInChargeName" required>
            <el-input v-model="formData.personInChargeName" placeholder="请输入负责人姓名" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 客户名称 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="客户名称" prop="customerName" required>
            <el-input v-model="formData.customerName" placeholder="请输入客户名称" />
            <div class="form-tip">企业客户输入全称,个人客户键入省市县名。</div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 身份证和所在地 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="身份证" prop="idCard">
            <el-input v-model="formData.idCard" placeholder="请填写身份证号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所在地" prop="location">
            <div class="location-input-group">
              <el-select v-model="formData.locationProvince" placeholder="请选择" style="width: 120px">
                <el-option label="北京市" value="北京市" />
                <el-option label="上海市" value="上海市" />
                <el-option label="广东省" value="广东省" />
                <el-option label="河南省" value="河南省" />
                <el-option label="江苏省" value="江苏省" />
                <el-option label="浙江省" value="浙江省" />
                <el-option label="山东省" value="山东省" />
                <el-option label="四川省" value="四川省" />
                <el-option label="湖北省" value="湖北省" />
                <el-option label="湖南省" value="湖南省" />
              </el-select>
              <el-input 
                v-model="formData.locationDetail" 
                :placeholder="formData.longitude && formData.latitude ? '已定位位置' : '必须填写四个字以上地址'" 
                style="flex: 1; margin-left: 8px;"
                :class="{ 'location-selected': formData.longitude && formData.latitude }"
              />
              <el-button type="primary" @click="handleMapLocation" style="margin-left: 8px;">
                <el-icon><Location /></el-icon>
                地图定位
              </el-button>
            </div>
            

          </el-form-item>
        </el-col>
      </el-row>

      <!-- 地图定位对话框 -->
      <MapLocationDialog
        v-model="showMap"
        :initial-address="formData.locationDetail"
        :initial-province="formData.locationProvince"
        @location-selected="handleLocationSelected"
      />

      <!-- 行业和统一社会信用代码 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属行业" prop="industry">
            <el-input v-model="formData.industry" placeholder="请输入所属行业" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="统一社会信用代码" prop="unifiedSocialCreditCode">
            <el-input v-model="formData.unifiedSocialCreditCode" placeholder="请填写统一社会信用代码(税号)" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 助记码和客户阶段 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="助记码" prop="mnemonicCode">
            <el-input v-model="formData.mnemonicCode" placeholder="请输入助记码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户阶段" prop="customerStage">
            <el-select v-model="formData.customerStage" placeholder="请选择客户阶段" style="width: 100%">
              <el-option label="潜在客户" value="潜在客户" />
              <el-option label="意向客户" value="意向客户" />
              <el-option label="成交客户" value="成交客户" />
              <el-option label="流失客户" value="流失客户" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 客户级别和客户集团分类 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="客户级别" prop="customerLevel" required>
            <el-select v-model="formData.customerLevel" placeholder="请选择客户级别" style="width: 100%">
              <el-option label="普通客户" value="普通客户" />
              <el-option label="VIP客户" value="VIP客户" />
              <el-option label="重要客户" value="重要客户" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户集团分类" prop="customerGroupClassification">
            <el-input v-model="formData.customerGroupClassification" placeholder="请输入客户集团分类" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 所属单位和使用状态 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属单位" prop="affiliatedUnit">
            <el-input v-model="formData.affiliatedUnit" placeholder="请输入所属单位" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="使用状态" prop="usageStatus" required>
            <el-radio-group v-model="formData.usageStatus">
              <el-radio value="启用">启用</el-radio>
              <el-radio value="停用">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 录入单位和使用单位 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="录入单位" prop="entryUnit">
            <el-input v-model="formData.entryUnit" placeholder="请输入录入单位" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="使用单位" prop="usageUnit">
            <el-input v-model="formData.usageUnit" placeholder="请输入使用单位" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 上级客户和客户归集档案 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="上级客户" prop="superiorCustomerId">
            <el-input-number
              v-model="formData.superiorCustomerId"
              placeholder="请输入上级客户ID"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="客户的归集档案" prop="customerCollectionFile">
            <el-input 
              v-model="formData.customerCollectionFile" 
              placeholder="一业务可以归集到设定客户档案"
            />
            <el-icon class="info-icon" color="#909399"><QuestionFilled /></el-icon>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 客户自定义标签 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="客户自定义标签" prop="customTags">
            <el-input v-model="formData.customTags" placeholder="请输入客户自定义标签" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 备注 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 服务人员 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属市场" prop="affiliatedMarket">
            <el-select v-model="formData.affiliatedMarket" placeholder="-- 请选择所属市场--" style="width: 100%">
              <el-option label="北京市场" value="北京市场" />
              <el-option label="上海市场" value="上海市场" />
              <el-option label="广州市场" value="广州市场" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="业务员" prop="salesperson">
            <el-select v-model="formData.salesperson" placeholder="---请选择业务员---" style="width: 100%">
              <el-option label="张三" value="张三" />
              <el-option label="李四" value="李四" />
              <el-option label="王五" value="王五" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 表单操作按钮 -->
      <el-form-item class="form-actions">
        <el-button type="primary" @click="handleSubmit" :loading="submitting" size="large">
          <el-icon><Check /></el-icon>
          提交保存
        </el-button>
        <el-button @click="handleReset" size="large">
          <el-icon><Refresh /></el-icon>
          重置表单
        </el-button>
        <el-button @click="handleCancel" size="large">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Check, Refresh, Close, Location, QuestionFilled, Delete } from '@element-plus/icons-vue'
import { customerApi, type AddCustomerParams } from '@/services/api'
import { mapService } from '@/services/mapService'
import MapLocationDialog from '@/components/MapLocationDialog.vue'

// 定义事件
const emit = defineEmits<{
  submitSuccess: []
  cancel: []
}>()

// 表单引用
const formRef = ref<FormInstance>()
const submitting = ref(false)
const showMap = ref(false)
const initialMapLocation = ref<{ lng: number; lat: number; address?: string } | null>(null)

  // 表单数据
  const formData = reactive({
    customerNumber: '',
    customerName: '',
    customerType: '个人',
    personInChargeName: '',
    personInChargePhone: '',
    idCard: '',
    locationProvince: '',
    locationDetail: '',
    industry: '',
    unifiedSocialCreditCode: '',
    mnemonicCode: '',
    customerStage: '',
    customerLevel: '',
    customerGroupClassification: '',
    usageStatus: '启用',
    entryUnit: '',
    usageUnit: '',
    superiorCustomerId: null as number | null,
    customTags: '',
    remarks: '',
    affiliatedMarket: '',
    salesperson: '',
    affiliatedUnit: '',
    customerCollectionFile: '',
    isArchived: false,
    // 原有字段保持兼容
    location: '',
    longitude: null as number | null,
    latitude: null as number | null
  })

// 表单验证规则
const rules: FormRules = {
  // customerNumber: [
  //   { required: true, message: '请输入客户编号', trigger: 'blur' },
  //   { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  // ],
  // customerName: [
  //   { required: true, message: '请输入客户名称', trigger: 'blur' },
  //   { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  // ],
  // customerType: [
  //   { required: true, message: '请选择客户类型', trigger: 'change' }
  // ],
  // customerLevel: [
  //   { required: true, message: '请选择客户级别', trigger: 'change' }
  // ],
  // personInChargeName: [
  //   { required: true, message: '请输入负责人姓名', trigger: 'blur' }
  // ],
  // personInChargePhone: [
  //   { required: true, message: '请输入负责人手机号', trigger: 'blur' },
  //   { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  // ],
  // usageStatus: [
  //   { required: true, message: '请选择使用状态', trigger: 'change' }
  // ],
  // idCard: [
  //   { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号', trigger: 'blur' }
  // ],
  // unifiedSocialCreditCode: [
  //   { pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/, message: '请输入正确的统一社会信用代码', trigger: 'blur' }
  // ]
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 合并地址信息
    formData.location = `${formData.locationProvince} ${formData.locationDetail}`.trim()
    
    // 验证地址信息
    if (!formData.location.trim()) {
      ElMessage.warning('请填写地址信息')
      submitting.value = false
      return
    }
    
    // 验证必填字段
    if (!formData.customerNumber.trim()) {
      ElMessage.warning('请填写客户编号')
      submitting.value = false
      return
    }
    
    if (!formData.customerName.trim()) {
      ElMessage.warning('请填写客户名称')
      submitting.value = false
      return
    }
    
    if (!formData.personInChargeName.trim()) {
      ElMessage.warning('请填写负责人姓名')
      submitting.value = false
      return
    }
    
    if (!formData.personInChargePhone.trim()) {
      ElMessage.warning('请填写负责人手机号')
      submitting.value = false
      return
    }
    
    // 如果没有经纬度，尝试自动获取
    if (!formData.longitude || !formData.latitude) {
      ElMessage.info('正在获取地址坐标...')
      const location = await mapService.searchAddress(formData.location)
      if (location) {
        formData.longitude = location.lng
        formData.latitude = location.lat
        ElMessage.success('已自动获取地址坐标')
      } else {
        ElMessage.warning('无法获取地址坐标，将使用默认值')
        formData.longitude = 116.397428
        formData.latitude = 39.90923
      }
    }
    
    // 构建提交数据，确保字段映射正确
    const submitData: AddCustomerParams = {
      customerNumber: formData.customerNumber,
      customerName: formData.customerName,
      customerType: formData.customerType,
      personInChargeName: formData.personInChargeName,
      personInChargePhone: formData.personInChargePhone,
      idCard: formData.idCard,
      location: formData.location,
      industry: formData.industry,
      unifiedSocialCreditCode: formData.unifiedSocialCreditCode,
      mnemonicCode: formData.mnemonicCode,
      customerStage: formData.customerStage,
      customerLevel: formData.customerLevel,
      customerGroupClassification: formData.customerGroupClassification,
      usageStatus: formData.usageStatus,
      entryUnit: formData.entryUnit,
      usageUnit: formData.usageUnit,
      superiorCustomerId: formData.superiorCustomerId,
      customTags: formData.customTags,
      remarks: formData.remarks,
      isArchived: formData.isArchived,
      longitude: formData.longitude,
      latitude: formData.latitude
    }
    
    // 调试信息
    console.log('提交的数据:', submitData);
    console.log('必填字段检查:');
    console.log('- customerNumber:', submitData.customerNumber);
    console.log('- customerName:', submitData.customerName);
    console.log('- customerType:', submitData.customerType);
    console.log('- personInChargeName:', submitData.personInChargeName);
    console.log('- personInChargePhone:', submitData.personInChargePhone);
    console.log('- location:', submitData.location);
    
    // 调用API添加客户
    const result = await customerApi.add(submitData)
    
    if (result.code === 200) {
      ElMessage.success('客户添加成功')
      emit('submitSuccess')
      handleReset()
    } else {
      ElMessage.error(result.message || '添加失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请检查表单信息')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    customerNumber: '',
    customerName: '',
    customerType: '个人',
    personInChargeName: '',
    personInChargePhone: '',
    idCard: '',
    locationProvince: '',
    locationDetail: '',
    industry: '',
    unifiedSocialCreditCode: '',
    mnemonicCode: '',
    customerStage: '',
    customerLevel: '',
    customerGroupClassification: '',
    usageStatus: '启用',
    entryUnit: '',
    usageUnit: '',
    superiorCustomerId: null,
    customTags: '',
    remarks: '',
    affiliatedMarket: '',
    salesperson: '',
    affiliatedUnit: '',
    customerCollectionFile: '',
    isArchived: false,
    location: '',
    longitude: null as number | null,
    latitude: null as number | null
  })
  showMap.value = false
  initialMapLocation.value = null
  ElMessage.info('表单已重置')
}

// 取消
const handleCancel = () => {
  emit('cancel')
}

// 处理位置选择
const handleLocationSelected = (location: { lng: number; lat: number; address: string }) => {
  formData.longitude = location.lng
  formData.latitude = location.lat
  
  // 解析地址信息，更新表单
  const addressParts = location.address.split(' ')
  if (addressParts.length >= 2) {
    formData.locationProvince = addressParts[0]
    formData.locationDetail = addressParts.slice(1).join(' ')
  } else {
    formData.locationDetail = location.address
  }
  
  showMap.value = false
  ElMessage.success('位置已设置')
}

// 处理位置清除
const handleLocationCleared = () => {
  formData.longitude = null
  formData.latitude = null
  formData.locationDetail = ''
  ElMessage.info('位置已清除')
}

// 清除位置
const clearLocation = () => {
  formData.longitude = null
  formData.latitude = null
  formData.location = ''
  formData.locationDetail = ''
  ElMessage.info('位置已清除')
}

// 处理地图定位
const handleMapLocation = () => {
  // 显示地图定位对话框
  showMap.value = true
}

// 生成客户编号
const generateCustomerNumber = () => {
  const timestamp = Date.now().toString().slice(-6) // 获取时间戳后6位
  const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0') // 3位随机数
  const prefix = formData.customerType === '企业' ? 'CUS' : 'PER' // 根据客户类型设置前缀
  formData.customerNumber = `${prefix}${timestamp}${random}`
  ElMessage.success('客户编号已生成')
}
</script>

<style scoped>
.customer-form {
  padding: 20px 0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-input-number) {
  width: 100%;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.customer-number-input {
  display: flex;
  align-items: center;
}

.location-input-group {
  display: flex;
  align-items: center;
}

.location-selected {
  border-color: #67C23A !important;
  background-color: #f0f9ff !important;
}

.location-selected:focus {
  border-color: #67C23A !important;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2) !important;
}

.map-container {
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  height: 500px;
}

.map-close {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.form-actions {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 10px;
  min-width: 120px;
}

.info-icon {
  margin-left: 8px;
  cursor: help;
}

/* 对话框样式 */
:deep(.customer-dialog .el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}
</style> 