# API端口修复指南

## 问题描述

当前系统中存在以下API端口问题：
1. 前端调用写API（如添加供应商）时使用了错误的端口号5102，导致404错误
2. 正确的写API端口号应该是5214
3. 读API端口号是5102

## 解决方案

我们提供了以下解决方案来修复API端口问题：

### 方法一：运行修复脚本（推荐）

1. 打开命令行终端，进入项目根目录
2. 运行修复脚本：
   ```bash
   node fix-api-ports.js
   ```
3. 重启开发服务器：
   ```bash
   npm run dev
   ```

### 方法二：手动修改文件

如果脚本执行失败，您可以按照以下步骤手动修改文件：

1. 创建 `src/services/apiConfig.ts` 文件：
   ```typescript
   // API配置文件 - 处理读写API的不同端口

   // API基础URL配置
   export const API_CONFIG = {
     // 写API (POST, PUT, DELETE)
     WRITE_API_BASE_URL: import.meta.env.VITE_API_WRITE_BASE_URL || 'http://localhost:5214',
     
     // 读API (GET)
     READ_API_BASE_URL: import.meta.env.VITE_API_READ_BASE_URL || 'http://localhost:5102'
   };

   /**
    * 根据请求方法获取对应的API基础URL
    * @param method 请求方法
    * @returns 对应的API基础URL
    */
   export function getApiBaseUrl(method: string): string {
     // 将方法转为大写以便比较
     const upperMethod = method.toUpperCase();
     
     // 如果是GET请求，使用读API的URL
     if (upperMethod === 'GET') {
       return API_CONFIG.READ_API_BASE_URL;
     }
     
     // 其他请求（POST, PUT, DELETE等）使用写API的URL
     return API_CONFIG.WRITE_API_BASE_URL;
   }

   export default API_CONFIG;
   ```

2. 修改 `src/services/api.ts` 文件：
   - 替换API基础配置：
     ```typescript
     // 导入API配置
     import { getApiBaseUrl } from './apiConfig'
     ```
   - 修改request函数中的URL构建部分：
     ```typescript
     // 根据请求方法获取对应的API基础URL
     const method = options.method || 'GET'
     const API_BASE_URL = getApiBaseUrl(method)
     
     const fullUrl = `${API_BASE_URL}${url}`
     console.log(`发送${method}请求到: ${fullUrl}`)
     ```

3. 创建 `.env.local` 文件：
   ```
   # 百度地图API密钥
   VITE_BAIDU_API_KEY=MqycZpQuzhz8JuMH40W3yfVImAYcJ1g9

   # API基础URL配置
   VITE_API_WRITE_BASE_URL=http://localhost:5214
   VITE_API_READ_BASE_URL=http://localhost:5102
   ```

## 验证修复效果

修复完成后，您可以通过以下步骤验证修复效果：

1. 打开浏览器开发者工具（按F12）
2. 切换到"网络"(Network)选项卡
3. 尝试添加供应商
4. 观察API请求是否发送到正确的端口（http://localhost:5214）
5. 确认请求是否成功（状态码200）

## 技术说明

### 端口配置

- **写API端口**：5214（用于POST, PUT, DELETE等写操作）
  - 对应服务：Agriculture.API.Write
  - 示例：添加供应商、添加客户等

- **读API端口**：5102（用于GET等读操作）
  - 对应服务：Agriculture.API.Read
  - 示例：获取供应商列表、获取客户详情等

### 实现原理

修复方案的核心是根据请求方法动态选择API基础URL：
1. 对于GET请求，使用读API的URL（http://localhost:5102）
2. 对于POST、PUT、DELETE等请求，使用写API的URL（http://localhost:5214）

这种方式可以让前端代码更加灵活，无需为不同类型的API调用编写不同的逻辑。

## 常见问题

### Q: 修改后仍然出现404错误怎么办？
A: 
1. 确认后端服务是否正常运行
2. 检查浏览器控制台中的请求URL是否正确
3. 尝试重启前端和后端服务

### Q: 环境变量不生效怎么办？
A:
1. 确认`.env.local`文件位于项目根目录
2. 重启开发服务器
3. 如果仍然不生效，检查代码中是否正确引用了环境变量

### Q: 如何在生产环境中配置API端口？
A:
创建`.env.production`文件，并设置生产环境的API端口：
```
VITE_API_WRITE_BASE_URL=http://your-production-write-api-url
VITE_API_READ_BASE_URL=http://your-production-read-api-url
``` 