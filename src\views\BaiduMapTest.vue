<template>
  <div class="baidu-map-test">
    <h1>百度地图API测试页面</h1>
    
    <div class="map-container">
      <BaiduMap 
        ref="mapRef"
        :initial-location="initialLocation"
        @location-selected="handleLocationSelected"
        @location-cleared="handleLocationCleared"
      />
    </div>
    
    <div class="test-panel">
      <h3>API测试</h3>
      <div class="test-form">
        <div class="form-item">
          <label>测试地址</label>
          <input v-model="testAddress" placeholder="输入测试地址，如：北京市海淀区清华大学" />
          <button @click="testRealSearch" :disabled="testing.search">
            {{ testing.search ? '测试中...' : '测试真实搜索' }}
          </button>
        </div>
        
        <div class="form-item">
          <label>测试坐标</label>
          <div class="coordinates-input">
            <input v-model="testCoordinates.lng" placeholder="经度，如：116.3" />
            <input v-model="testCoordinates.lat" placeholder="纬度，如：39.9" />
          </div>
          <button @click="testReverseGeocode" :disabled="testing.reverse">
            {{ testing.reverse ? '测试中...' : '测试逆地理编码' }}
          </button>
        </div>
      </div>
      
      <h3>测试结果</h3>
      <div class="test-results">
        <div v-if="testResults.length === 0" class="empty-results">
          <p>暂无测试结果</p>
        </div>
        
        <div v-else>
          <div v-for="(result, index) in testResults" :key="index" class="result-item">
            <div class="result-header" :class="{ 'success': result.success, 'error': !result.success }">
              {{ result.title }}
            </div>
            <div class="result-description">
              {{ result.description }}
            </div>
            <div v-if="result.data" class="result-data">
              <pre>{{ JSON.stringify(result.data, null, 2) }}</pre>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref } from 'vue'
import BaiduMap from '@/components/BaiduMap.vue'
import { mapService } from '@/services/mapService'

export default {
  components: {
    BaiduMap
  },
  setup() {
    // 初始位置 - 默认为北京
    const initialLocation = ref({
      lng: 116.397428,
      lat: 39.90923,
      address: '北京市'
    });

    // 地图组件引用
    const mapRef = ref(null);

    // 选中的位置
    const selectedLocation = ref(null);

    // 测试表单数据
    const testAddress = ref('北京市海淀区清华大学');
    const testCoordinates = ref({
      lng: 116.402398,
      lat: 39.906282
    });

    // 测试中状态
    const testing = ref({
      search: false,
      reverse: false
    });

    // 测试结果
    const testResults = ref([]);

    // 处理位置选择
    const handleLocationSelected = (location) => {
      selectedLocation.value = location;
      console.log('位置已选择:', location);
    };

    // 处理位置清除
    const handleLocationCleared = () => {
      selectedLocation.value = null;
      console.log('位置已清除');
    };

    // 测试真实搜索
    const testRealSearch = async () => {
      if (!testAddress.value) {
        alert('请输入测试地址');
        return;
      }
      
      testing.value.search = true;
      try {
        console.log('开始测试真实搜索:', testAddress.value);
        
        // 使用地图服务搜索地址
        const result = await mapService.searchAddress(testAddress.value);
        
        if (result) {
          // 更新地图位置
          if (mapRef.value) {
            mapRef.value.setLocation(result.lng, result.lat, result.address);
          }
          
          // 添加测试结果
          testResults.value.unshift({
            title: '地址搜索成功',
            success: true,
            description: `已找到地址: ${result.address}`,
            data: result,
            time: new Date()
          });
          
          alert('地址搜索成功');
        } else {
          testResults.value.unshift({
            title: '地址搜索失败',
            success: false,
            description: '未找到地址，请检查API密钥或网络连接',
            time: new Date()
          });
          
          alert('地址搜索失败');
        }
      } catch (error) {
        console.error('测试搜索时出错:', error);
        
        testResults.value.unshift({
          title: '地址搜索出错',
          success: false,
          description: error.message || '未知错误',
          time: new Date()
        });
        
        alert('测试出错: ' + (error.message || '未知错误'));
      } finally {
        testing.value.search = false;
      }
    };

    // 测试逆地理编码
    const testReverseGeocode = async () => {
      if (!testCoordinates.value.lng || !testCoordinates.value.lat) {
        alert('请输入测试坐标');
        return;
      }
      
      testing.value.reverse = true;
      try {
        console.log('开始测试逆地理编码:', testCoordinates.value);
        
        // 使用地图服务进行逆地理编码
        const address = await mapService.getAddressFromCoordinates(
          parseFloat(testCoordinates.value.lng),
          parseFloat(testCoordinates.value.lat)
        );
        
        if (address) {
          // 更新地图位置
          if (mapRef.value) {
            mapRef.value.setLocation(
              parseFloat(testCoordinates.value.lng),
              parseFloat(testCoordinates.value.lat),
              address
            );
          }
          
          // 添加测试结果
          testResults.value.unshift({
            title: '逆地理编码成功',
            success: true,
            description: `坐标对应地址: ${address}`,
            data: {
              lng: parseFloat(testCoordinates.value.lng),
              lat: parseFloat(testCoordinates.value.lat),
              address: address
            },
            time: new Date()
          });
          
          alert('逆地理编码成功');
        } else {
          testResults.value.unshift({
            title: '逆地理编码失败',
            success: false,
            description: '未能获取地址，请检查API密钥或网络连接',
            time: new Date()
          });
          
          alert('逆地理编码失败');
        }
      } catch (error) {
        console.error('测试逆地理编码时出错:', error);
        
        testResults.value.unshift({
          title: '逆地理编码出错',
          success: false,
          description: error.message || '未知错误',
          time: new Date()
        });
        
        alert('测试出错: ' + (error.message || '未知错误'));
      } finally {
        testing.value.reverse = false;
      }
    };

    return {
      initialLocation,
      mapRef,
      selectedLocation,
      testAddress,
      testCoordinates,
      testing,
      testResults,
      handleLocationSelected,
      handleLocationCleared,
      testRealSearch,
      testReverseGeocode
    };
  }
};
</script>

<style scoped>
.baidu-map-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.map-container {
  height: 500px;
  border: 1px solid #ddd;
  margin-bottom: 20px;
}

.test-panel {
  border: 1px solid #ddd;
  padding: 20px;
  background-color: #f9f9f9;
}

.test-form {
  margin-bottom: 20px;
}

.form-item {
  margin-bottom: 15px;
}

.form-item label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
}

.form-item input {
  width: 100%;
  padding: 8px;
  margin-bottom: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.coordinates-input {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.coordinates-input input {
  flex: 1;
}

button {
  padding: 8px 16px;
  background-color: #409EFF;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

button:disabled {
  background-color: #a0cfff;
  cursor: not-allowed;
}

.test-results {
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  margin-bottom: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  overflow: hidden;
}

.result-header {
  padding: 10px;
  font-weight: bold;
}

.result-header.success {
  background-color: #67C23A;
  color: white;
}

.result-header.error {
  background-color: #F56C6C;
  color: white;
}

.result-description {
  padding: 10px;
  background-color: white;
}

.result-data {
  padding: 10px;
  background-color: #f5f7fa;
  border-top: 1px solid #ddd;
  overflow-x: auto;
}

pre {
  margin: 0;
  white-space: pre-wrap;
}

.empty-results {
  text-align: center;
  color: #909399;
  padding: 20px 0;
}
</style> 