# 地图定位功能配置说明

## 功能概述

本项目集成了高德地图API，提供地图定位功能，用户可以通过点击地图来选择具体的地理位置。

## 配置步骤

### 1. 申请高德地图API密钥

1. 访问高德开放平台：https://lbs.amap.com/
2. 注册账号并登录
3. 创建应用，选择"Web端(JS API)"
4. 获取API密钥（Key）

### 2. 配置环境变量

在项目根目录创建 `.env.local` 文件（如果不存在），添加以下内容：

```env
# 高德地图API密钥
VITE_AMAP_KEY=your_actual_amap_key_here
```

**注意：** 请将 `your_actual_amap_key_here` 替换为您实际申请到的API密钥。

### 3. 重启开发服务器

配置完成后，需要重启开发服务器以使环境变量生效：

```bash
npm run dev
```

## 功能特性

- **地图显示**：集成高德地图，支持缩放、拖拽等操作
- **位置选择**：点击地图任意位置即可选择该位置
- **地址解析**：自动将坐标转换为详细地址信息
- **位置确认**：选择位置后可确认并应用到表单
- **位置清除**：支持清除已选择的位置

## 使用说明

1. 在客户信息表单中，点击"地图定位"按钮
2. 地图组件会弹出，显示当前地图
3. 点击地图上的任意位置来选择该位置
4. 系统会自动解析该位置的详细地址
5. 点击"确认位置"按钮将位置信息应用到表单
6. 点击"清除选择"按钮可以清除已选择的位置

## 技术实现

- 使用 `@amap/amap-jsapi-loader` 加载高德地图API
- 集成地址解析服务（Geocoder）
- 支持自定义地图标记
- 响应式设计，适配不同屏幕尺寸

## 注意事项

1. **API密钥安全**：请妥善保管您的API密钥，不要将其提交到版本控制系统
2. **使用限制**：高德地图API有使用限制，请参考官方文档
3. **网络要求**：地图功能需要网络连接才能正常使用
4. **浏览器兼容性**：建议使用现代浏览器以获得最佳体验

## 故障排除

### 地图无法加载
- 检查API密钥是否正确配置
- 确认网络连接正常
- 查看浏览器控制台是否有错误信息

### 地址解析失败
- 检查API密钥是否有地址解析权限
- 确认网络连接正常
- 尝试重新选择位置

### 地图显示异常
- 清除浏览器缓存
- 检查浏览器是否支持WebGL
- 尝试使用不同的浏览器 