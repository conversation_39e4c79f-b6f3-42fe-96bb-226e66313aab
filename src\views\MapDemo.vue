<template>
  <div class="map-demo">
    <el-card class="demo-card">
      <template #header>
        <div class="card-header">
          <h2>地图定位功能演示</h2>
          <el-tag :type="mapService.hasValidApiKey() ? 'success' : 'warning'">
            {{ mapService.hasValidApiKey() ? '高德地图API模式' : '模拟模式' }}
          </el-tag>
        </div>
      </template>
      
      <div class="demo-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="demo-section">
              <h3>地址搜索定位</h3>
              <p>输入地址，点击搜索按钮获取经纬度坐标</p>
              
              <el-form :model="searchForm" label-width="80px">
                <el-form-item label="地址">
                  <el-input 
                    v-model="searchForm.address" 
                    placeholder="请输入地址，如：北京市朝阳区"
                    @keyup.enter="handleSearch"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleSearch" :loading="searching">
                    搜索地址
                  </el-button>
                </el-form-item>
              </el-form>
              
              <div v-if="searchResult" class="search-result">
                <h4>搜索结果：</h4>
                <p><strong>地址：</strong>{{ searchResult.address }}</p>
                <p><strong>经度：</strong>{{ searchResult.lng.toFixed(6) }}</p>
                <p><strong>纬度：</strong>{{ searchResult.lat.toFixed(6) }}</p>
              </div>
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="demo-section">
              <h3>坐标转地址</h3>
              <p>输入经纬度坐标，获取对应的地址信息</p>
              
              <el-form :model="reverseForm" label-width="80px">
                <el-form-item label="经度">
                  <el-input v-model="reverseForm.lng" placeholder="请输入经度" />
                </el-form-item>
                <el-form-item label="纬度">
                  <el-input v-model="reverseForm.lat" placeholder="请输入纬度" />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="handleReverseSearch" :loading="reverseSearching">
                    获取地址
                  </el-button>
                </el-form-item>
              </el-form>
              
              <div v-if="reverseResult" class="reverse-result">
                <h4>地址信息：</h4>
                <p><strong>地址：</strong>{{ reverseResult }}</p>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <el-divider />
        
        <div class="demo-section">
          <h3>地图选择器组件</h3>
          <p>使用地图选择器组件进行位置选择</p>
          
          <el-button type="primary" @click="showMapSelector = true">
            打开地图选择器
          </el-button>
          
          <div v-if="selectedLocation" class="selected-location">
            <h4>已选择位置：</h4>
            <p><strong>地址：</strong>{{ selectedLocation.address }}</p>
            <p><strong>坐标：</strong>{{ selectedLocation.lng.toFixed(6) }}, {{ selectedLocation.lat.toFixed(6) }}</p>
          </div>
        </div>
        
        <el-divider />
        
        <div class="demo-section">
          <h3>使用说明</h3>
          <el-alert
            title="配置说明"
            type="info"
            :closable="false"
            show-icon
          >
            <template #default>
              <p>1. 要使用真实的高德地图API，请在项目根目录创建 <code>.env.local</code> 文件</p>
              <p>2. 添加配置：<code>VITE_AMAP_API_KEY=your_api_key_here</code></p>
              <p>3. 重启开发服务器使配置生效</p>
              <p>4. 未配置API密钥时将使用模拟模式</p>
            </template>
          </el-alert>
        </div>
      </div>
    </el-card>
    
    <!-- 地图选择器对话框 -->
    <el-dialog
      v-model="showMapSelector"
      title="地图位置选择"
      width="80%"
      :before-close="handleCloseMapSelector"
    >
      <BaiduMap
        :initial-location="selectedLocation"
        @location-selected="handleLocationSelected"
        @location-cleared="handleLocationCleared"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { mapService } from '@/services/mapService'
import BaiduMap from '@/components/BaiduMap.vue'

// 搜索表单
const searchForm = reactive({
  address: ''
})

// 逆地理编码表单
const reverseForm = reactive({
  lng: '',
  lat: ''
})

// 状态变量
const searching = ref(false)
const reverseSearching = ref(false)
const showMapSelector = ref(false)
const searchResult = ref<{ lng: number; lat: number; address: string } | null>(null)
const reverseResult = ref<string | null>(null)
const selectedLocation = ref<{ lng: number; lat: number; address: string } | null>(null)

// 处理地址搜索
const handleSearch = async () => {
  if (!searchForm.address.trim()) {
    ElMessage.warning('请输入地址')
    return
  }
  
  searching.value = true
  try {
    const result = await mapService.searchAddress(searchForm.address)
    if (result) {
      searchResult.value = result
      ElMessage.success('地址搜索成功')
    } else {
      ElMessage.warning('未找到该地址')
    }
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    searching.value = false
  }
}

// 处理逆地理编码
const handleReverseSearch = async () => {
  const lng = parseFloat(reverseForm.lng)
  const lat = parseFloat(reverseForm.lat)
  
  if (isNaN(lng) || isNaN(lat)) {
    ElMessage.warning('请输入有效的经纬度')
    return
  }
  
  reverseSearching.value = true
  try {
    const result = await mapService.getAddressFromCoordinates(lng, lat)
    if (result) {
      reverseResult.value = result
      ElMessage.success('地址获取成功')
    } else {
      ElMessage.warning('无法获取地址信息')
    }
  } catch (error) {
    console.error('逆地理编码失败:', error)
    ElMessage.error('获取地址失败，请重试')
  } finally {
    reverseSearching.value = false
  }
}

// 处理位置选择
const handleLocationSelected = (location: { lng: number; lat: number; address: string }) => {
  selectedLocation.value = location
  showMapSelector.value = false
  ElMessage.success('位置选择成功')
}

// 处理位置清除
const handleLocationCleared = () => {
  selectedLocation.value = null
  ElMessage.info('位置已清除')
}

// 关闭地图选择器
const handleCloseMapSelector = () => {
  showMapSelector.value = false
}
</script>

<style scoped>
.map-demo {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.demo-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #303133;
}

.demo-content {
  padding: 20px 0;
}

.demo-section {
  margin-bottom: 30px;
}

.demo-section h3 {
  color: #409EFF;
  margin-bottom: 10px;
}

.demo-section p {
  color: #606266;
  margin-bottom: 15px;
}

.search-result,
.reverse-result,
.selected-location {
  margin-top: 15px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 4px;
  border-left: 4px solid #409EFF;
}

.search-result h4,
.reverse-result h4,
.selected-location h4 {
  margin: 0 0 10px 0;
  color: #409EFF;
}

.search-result p,
.reverse-result p,
.selected-location p {
  margin: 5px 0;
  color: #606266;
}

code {
  background: #f0f0f0;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
}

:deep(.el-alert__content) {
  padding: 0;
}

:deep(.el-alert__content p) {
  margin: 5px 0;
  line-height: 1.5;
}
</style> 