// 地图API测试脚本
import fs from 'fs';
import path from 'path';

// 测试配置
const testConfig = {
  // 测试地址列表
  testAddresses: [
    '北京市天安门',
    '上海市外滩',
    '广州市广州塔',
    '深圳市世界之窗',
    '杭州市西湖',
    '南京市夫子庙',
    '武汉市黄鹤楼',
    '成都市宽窄巷子',
    '西安市大雁塔',
    '重庆市洪崖洞'
  ],
  
  // 测试坐标
  testCoordinates: [
    { lng: 116.397428, lat: 39.90923, name: '北京天安门' },
    { lng: 121.473701, lat: 31.230416, name: '上海市中心' },
    { lng: 113.264435, lat: 23.129163, name: '广州市中心' },
    { lng: 114.057868, lat: 22.543099, name: '深圳市中心' }
  ]
};

// 模拟地图服务（用于测试）
const mockMapService = {
  // 模拟地理编码
  searchAddress: async (address) => {
    console.log(`🔍 搜索地址: ${address}`);
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 500));
    
    // 模拟搜索结果
    const mockResults = {
      '北京市天安门': { lng: 116.397026, lat: 39.903487, address: '北京市东城区天安门广场' },
      '上海市外滩': { lng: 121.490317, lat: 31.236191, address: '上海市黄浦区外滩' },
      '广州市广州塔': { lng: 113.321275, lat: 23.106552, address: '广州市海珠区广州塔' },
      '深圳市世界之窗': { lng: 113.973129, lat: 22.540503, address: '深圳市南山区世界之窗' },
      '杭州市西湖': { lng: 120.155070, lat: 30.274084, address: '杭州市西湖区西湖' },
      '南京市夫子庙': { lng: 118.796877, lat: 32.060255, address: '南京市秦淮区夫子庙' },
      '武汉市黄鹤楼': { lng: 114.298572, lat: 30.584355, address: '武汉市武昌区黄鹤楼' },
      '成都市宽窄巷子': { lng: 104.065735, lat: 30.659462, address: '成都市青羊区宽窄巷子' },
      '西安市大雁塔': { lng: 108.948024, lat: 34.263161, address: '西安市雁塔区大雁塔' },
      '重庆市洪崖洞': { lng: 106.551556, lat: 29.563009, address: '重庆市渝中区洪崖洞' }
    };
    
    const result = mockResults[address];
    if (result) {
      console.log(`✅ 找到位置: ${result.address} (${result.lng}, ${result.lat})`);
      return result;
    } else {
      console.log(`❌ 未找到地址: ${address}`);
      return null;
    }
  },
  
  // 模拟逆地理编码
  getAddressFromCoordinates: async (lng, lat) => {
    console.log(`📍 逆地理编码: (${lng}, ${lat})`);
    
    // 模拟API调用延迟
    await new Promise(resolve => setTimeout(resolve, 300));
    
    // 模拟逆地理编码结果
    const mockAddresses = {
      '116.397428,39.90923': '北京市东城区天安门广场',
      '121.473701,31.230416': '上海市黄浦区人民广场',
      '113.264435,23.129163': '广州市越秀区北京路',
      '114.057868,22.543099': '深圳市福田区市民中心'
    };
    
    const key = `${lng.toFixed(6)},${lat.toFixed(6)}`;
    const address = mockAddresses[key] || `位置(${lng.toFixed(6)}, ${lat.toFixed(6)})`;
    
    console.log(`✅ 逆地理编码结果: ${address}`);
    return address;
  },
  
  // 检查API状态
  hasValidApiKey: () => {
    const hasKey = process.env.VITE_BAIDU_API_KEY && 
                   process.env.VITE_BAIDU_API_KEY !== 'your_baidu_api_key' &&
                   process.env.VITE_BAIDU_API_KEY.length > 10;
    
    console.log(`🔑 API密钥状态: ${hasKey ? '有效' : '无效'}`);
    return hasKey;
  }
};

// 测试地理编码功能
async function testGeocoding() {
  console.log('\n🌍 测试地理编码功能');
  console.log('='.repeat(50));
  
  for (const address of testConfig.testAddresses) {
    try {
      const result = await mockMapService.searchAddress(address);
      if (result) {
        console.log(`✅ ${address} -> ${result.address} (${result.lng}, ${result.lat})`);
      } else {
        console.log(`❌ ${address} -> 未找到`);
      }
    } catch (error) {
      console.log(`❌ ${address} -> 错误: ${error.message}`);
    }
    
    // 添加延迟避免过快请求
    await new Promise(resolve => setTimeout(resolve, 200));
  }
}

// 测试逆地理编码功能
async function testReverseGeocoding() {
  console.log('\n📍 测试逆地理编码功能');
  console.log('='.repeat(50));
  
  for (const coord of testConfig.testCoordinates) {
    try {
      const address = await mockMapService.getAddressFromCoordinates(coord.lng, coord.lat);
      console.log(`✅ ${coord.name} (${coord.lng}, ${coord.lat}) -> ${address}`);
    } catch (error) {
      console.log(`❌ ${coord.name} -> 错误: ${error.message}`);
    }
    
    // 添加延迟避免过快请求
    await new Promise(resolve => setTimeout(resolve, 200));
  }
}

// 测试API状态检查
function testApiStatus() {
  console.log('\n🔑 测试API状态检查');
  console.log('='.repeat(50));
  
  const status = mockMapService.hasValidApiKey();
  console.log(`API密钥状态: ${status ? '✅ 有效' : '❌ 无效'}`);
  
  if (!status) {
    console.log('💡 建议配置百度地图API密钥以获得更准确的位置信息');
    console.log('📖 请参考 BAIDU_MAP_SETUP.md 文件进行配置');
  }
}

// 生成测试报告
function generateTestReport() {
  console.log('\n📊 测试报告');
  console.log('='.repeat(50));
  
  const report = {
    timestamp: new Date().toISOString(),
    totalTests: testConfig.testAddresses.length + testConfig.testCoordinates.length + 1,
    geocodingTests: testConfig.testAddresses.length,
    reverseGeocodingTests: testConfig.testCoordinates.length,
    apiStatusTest: 1,
    recommendations: []
  };
  
  if (!mockMapService.hasValidApiKey()) {
    report.recommendations.push('配置百度地图API密钥');
    report.recommendations.push('参考 BAIDU_MAP_SETUP.md 进行设置');
  }
  
  console.log(`测试时间: ${report.timestamp}`);
  console.log(`总测试数: ${report.totalTests}`);
  console.log(`地理编码测试: ${report.geocodingTests}`);
  console.log(`逆地理编码测试: ${report.reverseGeocodingTests}`);
  console.log(`API状态测试: ${report.apiStatusTest}`);
  
  if (report.recommendations.length > 0) {
    console.log('\n💡 建议:');
    report.recommendations.forEach(rec => console.log(`  - ${rec}`));
  }
  
  return report;
}

// 主测试函数
async function runTests() {
  console.log('🚀 开始地图API功能测试');
  console.log('='.repeat(60));
  
  try {
    // 测试API状态
    testApiStatus();
    
    // 测试地理编码
    await testGeocoding();
    
    // 测试逆地理编码
    await testReverseGeocoding();
    
    // 生成测试报告
    const report = generateTestReport();
    
    console.log('\n✅ 测试完成！');
    console.log('='.repeat(60));
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 直接运行测试
runTests();

export {
  mockMapService,
  testConfig,
  runTests
}; 