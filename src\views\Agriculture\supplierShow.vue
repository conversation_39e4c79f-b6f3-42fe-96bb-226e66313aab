<template>
  <div class="supplier-show">
    <div class="header">
      <div class="header-left">
        <h2>供应商管理</h2>
        <p class="header-desc">管理供应商信息，包括添加、查看、编辑供应商详情</p>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-area">
      <el-form :model="searchForm" inline>
        <el-form-item label="供应商编号">
          <el-input v-model="searchForm.supplierNumber" placeholder="请输入供应商编号" clearable />
        </el-form-item>
        <el-form-item label="供应商名称">
          <el-input v-model="searchForm.supplierName" placeholder="请输入供应商名称" clearable />
        </el-form-item>
        <el-form-item label="供应商类型">
          <el-select v-model="searchForm.supplierType" placeholder="请选择" clearable>
            <el-option label="个人" value="个人" />
            <el-option label="企业" value="企业" />
          </el-select>
        </el-form-item>
        <el-form-item label="使用状态">
          <el-select v-model="searchForm.usageStatus" placeholder="请选择" clearable>
            <el-option label="启用" :value="true" />
            <el-option label="停用" :value="false" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">搜索</el-button>
          <el-button @click="handleReset">重置</el-button>
          <el-button type="success" @click="showAddDialog = true">
            <el-icon>
              <Plus />
            </el-icon>
            新增供应商
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <!-- 表格区域 -->
    <div class="table-area">
      <el-table :data="supplierList" v-loading="loading" stripe>
        <el-table-column prop="supplierNumber" label="供应商编号" width="120" />
        <el-table-column prop="supplierName" label="供应商名称" width="200" />
        <el-table-column prop="supplierType" label="供应商类型" width="100" />
        <el-table-column prop="personInChargeName" label="负责人" width="100" />
        <el-table-column prop="personInChargePhone" label="联系电话" width="130" />
        <el-table-column prop="location" label="所在地" width="150" />
        <el-table-column prop="industry" label="所属行业" width="120" />
        <el-table-column prop="usageStatus" label="使用状态" width="80">
          <template #default="{ row }">
            <el-tag :type="row.usageStatus ? 'success' : 'danger'">
              {{ row.usageStatus ? '启用' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" size="small" @click="handleViewDetail(row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination v-model:current-page="pagination.pageIndex" v-model:page-size="pagination.pageSize"
          :page-sizes="[4, 8, 12, 16]" :total="pagination.total" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 添加供应商对话框 -->
    <el-dialog v-model="showAddDialog" title="供应商-新增" width="80%" :before-close="handleCloseAdd"
      class="supplier-dialog">
      <SupplierForm ref="supplierFormRef" @submit-success="handleAddSuccess" @cancel="handleCancel" />
    </el-dialog>

    <!-- 详情对话框 -->
    <el-dialog v-model="showDetailDialog" title="供应商详情" width="800px">
      <SupplierDetail v-if="showDetailDialog" :supplier-id="selectedSupplierId" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import SupplierForm from './components/SupplierForm.vue'
import SupplierDetail from './components/SupplierDetail.vue'
import { supplierApi, type Supplier } from '@/services/api'

// 响应式数据
const loading = ref(false)
const supplierList = ref<Supplier[]>([])
const showAddDialog = ref(false)
const showDetailDialog = ref(false)
const selectedSupplierId = ref(0)
const supplierFormRef = ref()

// 搜索表单
const searchForm = reactive({
  supplierNumber: '',
  supplierName: '',
  supplierType: '',
  usageStatus: null as boolean | null
})

// 分页信息
const pagination = reactive({
  pageIndex: 1,
  pageSize: 4,
  total: 0
})

// 获取供应商列表
const getSupplierList = async () => {
  loading.value = true
  try {
    const params = {
      ...searchForm,
      pageIndex: pagination.pageIndex,
      pageSize: pagination.pageSize
    }

    console.log('发送API请求参数:', params)
    const result = await supplierApi.getList(params)
    console.log('API响应结果:', result)

    // 适配后端返回的数据结构
    if (result.code === 200) {
      console.log('API调用成功，数据:', result.data)
      supplierList.value = result.data.pageData || []
      pagination.total = result.data.totalCount || 0
      console.log('设置供应商列表:', supplierList.value)
      console.log('设置总数:', pagination.total)
    } else {
      console.error('API调用失败:', result.message)
      ElMessage.error(result.message || '获取供应商列表失败')
    }
  } catch (error) {
    console.error('获取供应商列表错误:', error)
    ElMessage.error('网络错误，请稍后重试')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.pageIndex = 1
  getSupplierList()
}

// 重置搜索
const handleReset = () => {
  Object.assign(searchForm, {
    supplierNumber: '',
    supplierName: '',
    supplierType: '',
    usageStatus: null
  })
  pagination.pageIndex = 1
  getSupplierList()
}

// 分页大小改变
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.pageIndex = 1
  getSupplierList()
}

// 当前页改变
const handleCurrentChange = (page: number) => {
  pagination.pageIndex = page
  getSupplierList()
}

// 查看详情
const handleViewDetail = (row: any) => {
  selectedSupplierId.value = row.id
  showDetailDialog.value = true
}



// 添加成功回调
const handleAddSuccess = () => {
  showAddDialog.value = false
  getSupplierList()
  ElMessage.success('供应商添加成功')
}

// 关闭添加对话框
const handleCloseAdd = (done: () => void) => {
  if (supplierFormRef.value) {
    supplierFormRef.value.handleReset()
  }
  done()
}

// 处理取消事件
const handleCancel = () => {
  showAddDialog.value = false
}

// 格式化日期
const formatDate = (date: string) => {
  if (!date) return ''
  return new Date(date).toLocaleString()
}

// 组件挂载时获取数据
onMounted(() => {
  getSupplierList()
})
</script>

<style scoped>
.supplier-show {
  height: 100%;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  color: white;
}

.header-left h2 {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
}

.header-desc {
  margin: 0;
  opacity: 0.9;
  font-size: 14px;
}

.search-area {
  background: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.search-area :deep(.el-form) {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: flex-end;
}

.search-area :deep(.el-form-item) {
  margin-bottom: 0;
  margin-right: 0;
}

.table-area {
  background: white;
  border-radius: 8px;
  padding: 20px;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}

:deep(.el-form-item__label) {
  min-width: 80px;
  text-align: right;
  padding-right: 8px;
}

:deep(.el-select) {
  min-width: 120px;
}

:deep(.el-input) {
  min-width: 150px;
}

:deep(.el-table) {
  margin-bottom: 20px;
}

.drawer-content {
  padding: 20px;
  height: calc(100vh - 120px);
  overflow-y: auto;
}
</style>
