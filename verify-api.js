// API服务验证脚本

// 百度地图API配置
const BAIDU_API_KEY = 'MqycZpQuzhz8JuMH40W3yfVImAYcJ1g9'
const BAIDU_BASE_URL = 'https://api.map.baidu.com'

// 测试地址
const testAddresses = [
  '信阳职业技术学院',
  '北京市天安门',
  '上海市外滩'
]

// 验证API服务状态
async function verifyApiService() {
  console.log('🔍 验证百度地图API服务状态');
  console.log('='.repeat(50));
  
  console.log(`API密钥: ${BAIDU_API_KEY.substring(0, 8)}...`);
  console.log(`密钥长度: ${BAIDU_API_KEY.length}`);
  console.log(`API基础URL: ${BAIDU_BASE_URL}`);
  
  try {
    console.log('\n🌐 测试API连接...');
    
    const testUrl = `${BAIDU_BASE_URL}/geocoding/v3/?address=北京&output=json&ak=${BAIDU_API_KEY}`;
    console.log(`请求URL: ${testUrl}`);
    
    const response = await fetch(testUrl);
    console.log(`响应状态: ${response.status} ${response.statusText}`);
    
    const data = await response.json();
    console.log('API响应:', JSON.stringify(data, null, 2));
    
    if (data.status === 0) {
      console.log('\n✅ API服务正常！');
      console.log('🎉 恭喜！您的百度地图API服务已经启用');
      console.log('📝 现在可以使用真实的地理编码服务了');
      return true;
    } else if (data.status === 240 && data.message.includes('APP 服务被禁用')) {
      console.log('\n❌ API服务被禁用');
      console.log('💡 需要手动启用服务，请参考 ENABLE_API_SERVICES.md');
      return false;
    } else {
      console.log(`\n❌ API错误: ${data.message}`);
      console.log('💡 请检查API密钥和应用配置');
      return false;
    }
    
  } catch (error) {
    console.log(`\n❌ 网络错误: ${error.message}`);
    console.log('💡 请检查网络连接');
    return false;
  }
}

// 测试地理编码功能
async function testGeocoding(apiEnabled) {
  console.log('\n🌍 测试地理编码功能');
  console.log('='.repeat(50));
  
  for (const address of testAddresses) {
    try {
      console.log(`\n搜索地址: ${address}`);
      
      if (apiEnabled) {
        // 使用真实API
        const url = `${BAIDU_BASE_URL}/geocoding/v3/?address=${encodeURIComponent(address)}&output=json&ak=${BAIDU_API_KEY}`;
        const response = await fetch(url);
        const data = await response.json();
        
        if (data.status === 0 && data.result) {
          const result = {
            lng: data.result.location.lng,
            lat: data.result.location.lat,
            address: data.result.formatted_address
          };
          console.log(`✅ 真实API结果: ${result.address} (${result.lng}, ${result.lat})`);
        } else {
          console.log(`❌ API错误: ${data.message}`);
        }
      } else {
        // 使用模拟数据
        const mockResult = getMockResult(address);
        console.log(`📝 模拟数据结果: ${mockResult.address} (${mockResult.lng}, ${mockResult.lat})`);
      }
      
    } catch (error) {
      console.log(`❌ 搜索失败: ${error.message}`);
    }
    
    // 添加延迟
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
}

// 获取模拟数据结果
function getMockResult(address) {
  const mockLocations = {
    '信阳职业技术学院': { lng: 114.092787, lat: 32.147015, address: '河南省信阳市信阳职业技术学院' },
    '北京市天安门': { lng: 116.397026, lat: 39.903487, address: '北京市东城区天安门广场' },
    '上海市外滩': { lng: 121.490317, lat: 31.236191, address: '上海市黄浦区外滩' }
  };
  
  for (const [key, location] of Object.entries(mockLocations)) {
    if (address.includes(key)) {
      return location;
    }
  }
  
  return { lng: 116.397428, lat: 39.90923, address: '北京市' };
}

// 提供下一步建议
function provideNextSteps(apiEnabled) {
  console.log('\n💡 下一步建议');
  console.log('='.repeat(50));
  
  if (apiEnabled) {
    console.log('✅ API服务已启用，您可以：');
    console.log('1. 启动项目：npm run dev');
    console.log('2. 测试地图定位功能');
    console.log('3. 享受真实的地理编码服务');
    console.log('4. 获得精确的坐标信息');
  } else {
    console.log('❌ API服务未启用，您需要：');
    console.log('1. 访问：https://lbsyun.baidu.com/');
    console.log('2. 登录并进入应用管理');
    console.log('3. 启用"地理编码"和"逆地理编码"服务');
    console.log('4. 配置域名白名单：localhost, 127.0.0.1');
    console.log('5. 等待5-10分钟让配置生效');
    console.log('6. 重新运行此脚本验证');
    console.log('\n📖 详细步骤请参考：ENABLE_API_SERVICES.md');
  }
}

// 主函数
async function main() {
  console.log('🚀 百度地图API服务验证');
  console.log('='.repeat(60));
  
  try {
    const apiEnabled = await verifyApiService();
    await testGeocoding(apiEnabled);
    provideNextSteps(apiEnabled);
    
    console.log('\n✅ 验证完成！');
    console.log('='.repeat(60));
    
  } catch (error) {
    console.error('❌ 验证过程中发生错误:', error);
  }
}

// 运行验证
main(); 