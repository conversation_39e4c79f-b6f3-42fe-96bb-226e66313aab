<template>
  <div class="map-selector">
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索地址或地点"
        class="search-input"
        @keyup.enter="handleSearch"
      >
        <template #append>
          <el-button @click="handleSearch" :loading="searching">
            <el-icon><Search /></el-icon>
          </el-button>
        </template>
      </el-input>
    </div>

    <!-- 地图容器 -->
    <div class="map-container">
      <div class="map-placeholder">
        <el-icon size="48" color="#409EFF"><Location /></el-icon>
        <p>地图组件 - 点击选择位置</p>
        <p class="map-copyright">高德地图 © 2023 AutoNavi GS(2021)6375号</p>
        <div class="map-instructions">
          <p>请配置高德地图API密钥以启用完整功能</p>
          <p>当前为演示模式，可手动输入地址信息</p>
        </div>
      </div>
    </div>
    
    <!-- 地图控制按钮 -->
    <div class="map-controls">
      <el-button type="primary" size="small" @click="confirmLocation" :disabled="!selectedLocation">
        <el-icon><Check /></el-icon>
        确认位置
      </el-button>
      <el-button size="small" @click="clearLocation">
        <el-icon><Delete /></el-icon>
        清除选择
      </el-button>
      <el-button size="small" @click="getCurrentLocation" :loading="gettingLocation">
        <el-icon><Location /></el-icon>
        我的位置
      </el-button>
    </div>

    <!-- 位置信息显示 -->
    <div v-if="selectedLocation" class="location-info">
      <div class="location-header">
        <el-icon color="#409EFF"><Location /></el-icon>
        <span>已选择位置</span>
      </div>
      <div class="location-details">
        <p class="address"><strong>地址：</strong>{{ selectedLocation.address }}</p>
        <p class="coordinates">
          <strong>坐标：</strong>
          {{ selectedLocation.lng.toFixed(6) }}, {{ selectedLocation.lat.toFixed(6) }}
        </p>
        <p class="api-status">
          <strong>API状态：</strong>
          <el-tag :type="mapService.hasValidApiKey() ? 'success' : 'warning'" size="small">
            {{ mapService.hasValidApiKey() ? '高德地图API' : '模拟模式' }}
          </el-tag>
        </p>
      </div>
    </div>

    <!-- 手动输入位置 -->
    <div class="manual-location">
      <el-divider>手动输入位置</el-divider>
      <el-form :model="manualLocation" label-width="80px">
        <el-form-item label="经度">
          <el-input v-model="manualLocation.lng" placeholder="请输入经度" />
        </el-form-item>
        <el-form-item label="纬度">
          <el-input v-model="manualLocation.lat" placeholder="请输入纬度" />
        </el-form-item>
        <el-form-item label="地址">
          <el-input v-model="manualLocation.address" placeholder="请输入详细地址" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="setManualLocation">设置位置</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Check, Delete, Location } from '@element-plus/icons-vue'
import { mapService } from '@/services/mapService'

// 定义props
interface Props {
  initialLocation?: {
    lng: number
    lat: number
    address?: string
  }
}

const props = withDefaults(defineProps<Props>(), {
  initialLocation: undefined
})

// 定义事件
const emit = defineEmits<{
  locationSelected: [location: { lng: number; lat: number; address: string }]
  locationCleared: []
}>()

// 响应式数据
const selectedLocation = ref<{ lng: number; lat: number; address: string } | null>(null)
const searchKeyword = ref('')
const searching = ref(false)
const gettingLocation = ref(false)
const manualLocation = ref({
  lng: '',
  lat: '',
  address: ''
})

// 处理搜索
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  
  searching.value = true
  try {
    // 使用地图服务搜索地址
    const location = await mapService.searchAddress(searchKeyword.value)
    
    if (location) {
      setLocation(location.lng, location.lat, location.address)
      ElMessage.success('已找到位置')
    } else {
      ElMessage.warning('未找到该地址，请尝试其他关键词')
    }
    
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    searching.value = false
  }
}

// 设置位置
const setLocation = (lng: number, lat: number, address: string) => {
  selectedLocation.value = { lng, lat, address }
}

// 获取当前位置
const getCurrentLocation = () => {
  gettingLocation.value = true
  
  if (!navigator.geolocation) {
    ElMessage.error('浏览器不支持地理定位')
    gettingLocation.value = false
    return
  }
  
  navigator.geolocation.getCurrentPosition(
    (position) => {
      const lat = position.coords.latitude
      const lng = position.coords.longitude
      
      setLocation(lng, lat, '当前位置')
      ElMessage.success('已定位到当前位置')
      gettingLocation.value = false
    },
    (error) => {
      console.error('获取位置失败:', error)
      ElMessage.error('获取位置失败，请检查定位权限')
      gettingLocation.value = false
    }
  )
}

// 设置手动输入的位置
const setManualLocation = () => {
  const lng = parseFloat(manualLocation.value.lng)
  const lat = parseFloat(manualLocation.value.lat)
  const address = manualLocation.value.address
  
  if (isNaN(lng) || isNaN(lat)) {
    ElMessage.error('请输入有效的经纬度')
    return
  }
  
  if (!address.trim()) {
    ElMessage.error('请输入地址')
    return
  }
  
  setLocation(lng, lat, address)
  ElMessage.success('位置已设置')
}

// 确认位置
const confirmLocation = () => {
  if (selectedLocation.value) {
    emit('locationSelected', selectedLocation.value)
    ElMessage.success('位置已确认')
  }
}

// 清除位置
const clearLocation = () => {
  selectedLocation.value = null
  searchKeyword.value = ''
  manualLocation.value = { lng: '', lat: '', address: '' }
  emit('locationCleared')
  ElMessage.info('位置已清除')
}

// 监听初始位置变化
watch(() => props.initialLocation, (newLocation) => {
  if (newLocation) {
    setLocation(newLocation.lng, newLocation.lat, newLocation.address || '')
  }
}, { deep: true })

// 组件挂载时设置初始位置
onMounted(() => {
  if (props.initialLocation) {
    setLocation(props.initialLocation.lng, props.initialLocation.lat, props.initialLocation.address || '')
  }
})
</script>

<style scoped>
.map-selector {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
}

.search-bar {
  margin-bottom: 12px;
}

.search-input {
  width: 100%;
}

.map-container {
  width: 100%;
  height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.map-placeholder {
  height: 100%;
  background: #f5f7fa;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  padding: 20px;
}

.map-placeholder p {
  margin: 8px 0 0 0;
  font-size: 14px;
}

.map-copyright {
  font-size: 12px !important;
  color: #c0c4cc !important;
  margin-top: 16px !important;
}

.map-instructions {
  margin-top: 20px;
  text-align: center;
  max-width: 300px;
}

.map-instructions p {
  margin: 4px 0;
  font-size: 12px;
  color: #909399;
}

.map-controls {
  margin-top: 12px;
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.location-info {
  margin-top: 12px;
  padding: 12px;
  background: #f0f9ff;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
}

.location-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 600;
  color: #409EFF;
}

.location-details p {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
}

.location-details .address {
  color: #303133;
  font-weight: 500;
}

.location-details .coordinates {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #909399;
}

.manual-location {
  margin-top: 20px;
  padding: 16px;
  background: #fafafa;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
}

@media (max-width: 768px) {
  .map-controls {
    flex-direction: column;
  }
  
  .map-controls .el-button {
    width: 100%;
  }
}
</style> 