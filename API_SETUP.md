# 百度地图API配置说明

## 快速配置

1. **申请API密钥**
   - 访问 https://lbsyun.baidu.com/
   - 注册账号并创建应用
   - 选择"Web端(JS API)"类型
   - 复制API密钥

2. **配置环境变量**
   - 在项目根目录创建 `.env.local` 文件
   - 添加：`VITE_BAIDU_API_KEY=your_api_key_here`
   - 重启开发服务器

3. **验证配置**
   - 访问 `/search-test` 页面
   - 查看API状态是否为"真实API"
   - 测试搜索功能

## 测试页面

- `/search-test` - 搜索功能测试
- `/test-map` - 地图功能测试
- `/map-demo` - 功能演示

## 常见问题

- 如果显示"模拟模式"，说明API密钥未配置
- 如果搜索失败，检查网络连接和API密钥有效性
- 确保重启开发服务器使配置生效 