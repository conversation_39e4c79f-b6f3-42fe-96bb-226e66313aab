// 真实API测试脚本

// 百度地图API配置
const BAIDU_API_KEY = 'MqycZpQuzhz8JuMH40W3yfVImAYcJ1g9'
const BAIDU_BASE_URL = 'https://api.map.baidu.com'

// 测试地址列表
const testAddresses = [
  '北京市天安门',
  '上海市外滩',
  '广州市广州塔',
  '深圳市世界之窗',
  '杭州市西湖'
]

// 测试坐标
const testCoordinates = [
  { lng: 116.397428, lat: 39.90923, name: '北京天安门' },
  { lng: 121.473701, lat: 31.230416, name: '上海市中心' }
]

// 测试地理编码
async function testGeocoding() {
  console.log('\n🌍 测试真实地理编码API');
  console.log('='.repeat(50));
  
  for (const address of testAddresses) {
    try {
      console.log(`🔍 搜索地址: ${address}`);
      
      const url = `${BAIDU_BASE_URL}/geocoding/v3/?address=${encodeURIComponent(address)}&output=json&ak=${BAIDU_API_KEY}`;
      
      const response = await fetch(url);
      const data = await response.json();
      
      if (data.status === 0 && data.result) {
        const result = {
          lng: data.result.location.lng,
          lat: data.result.location.lat,
          address: data.result.formatted_address
        };
        console.log(`✅ ${address} -> ${result.address} (${result.lng}, ${result.lat})`);
      } else {
        console.log(`❌ ${address} -> API错误: ${data.message || '未知错误'}`);
      }
      
      // 添加延迟避免过快请求
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.log(`❌ ${address} -> 网络错误: ${error.message}`);
    }
  }
}

// 测试逆地理编码
async function testReverseGeocoding() {
  console.log('\n📍 测试真实逆地理编码API');
  console.log('='.repeat(50));
  
  for (const coord of testCoordinates) {
    try {
      console.log(`📍 逆地理编码: ${coord.name} (${coord.lng}, ${coord.lat})`);
      
      const url = `${BAIDU_BASE_URL}/reverse_geocoding/v3/?location=${coord.lat},${coord.lng}&output=json&ak=${BAIDU_API_KEY}`;
      
      const response = await fetch(url);
      const data = await response.json();
      
      if (data.status === 0 && data.result) {
        console.log(`✅ ${coord.name} -> ${data.result.formatted_address}`);
      } else {
        console.log(`❌ ${coord.name} -> API错误: ${data.message || '未知错误'}`);
      }
      
      // 添加延迟避免过快请求
      await new Promise(resolve => setTimeout(resolve, 1000));
      
    } catch (error) {
      console.log(`❌ ${coord.name} -> 网络错误: ${error.message}`);
    }
  }
}

// 检查API密钥
function checkApiKey() {
  console.log('\n🔑 检查API密钥');
  console.log('='.repeat(50));
  console.log(`API密钥: ${BAIDU_API_KEY.substring(0, 8)}...`);
  console.log(`密钥长度: ${BAIDU_API_KEY.length}`);
  console.log(`密钥状态: ${BAIDU_API_KEY && BAIDU_API_KEY.length > 10 ? '✅ 有效' : '❌ 无效'}`);
}

// 主测试函数
async function runRealApiTests() {
  console.log('🚀 开始真实API测试');
  console.log('='.repeat(60));
  
  try {
    // 检查API密钥
    checkApiKey();
    
    // 测试地理编码
    await testGeocoding();
    
    // 测试逆地理编码
    await testReverseGeocoding();
    
    console.log('\n✅ 真实API测试完成！');
    console.log('='.repeat(60));
    
  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
}

// 运行测试
runRealApiTests(); 