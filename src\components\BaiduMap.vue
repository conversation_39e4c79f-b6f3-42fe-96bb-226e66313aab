<template>
  <div class="baidu-map-container">
    <!-- 地图容器 -->
    <div ref="mapContainer" class="map-container"></div>
    
    <!-- 搜索栏 -->
    <div class="search-bar">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索地址或地点"
        class="search-input"
        @keyup.enter="handleSearch"
      >
        <template #append>
          <el-button @click="handleSearch" :loading="searching">
            <el-icon><Search /></el-icon>
          </el-button>
        </template>
      </el-input>
    </div>
    
    <!-- 地图控制按钮 -->
    <div class="map-controls">
      <el-button type="primary" size="small" @click="confirmLocation" :disabled="!selectedLocation">
        <el-icon><Check /></el-icon>
        确认位置
      </el-button>
      <el-button size="small" @click="clearLocation">
        <el-icon><Delete /></el-icon>
        清除选择
      </el-button>
      <el-button size="small" @click="getCurrentLocation" :loading="gettingLocation">
        <el-icon><Location /></el-icon>
        我的位置
      </el-button>
    </div>
    
    <!-- 位置信息显示 -->
    <div v-if="selectedLocation" class="location-info">
      <div class="location-header">
        <el-icon color="#409EFF"><Location /></el-icon>
        <span>已选择位置</span>
      </div>
      <div class="location-details">
        <p class="address"><strong>地址：</strong>{{ selectedLocation.address }}</p>
        <p class="coordinates">
          <strong>坐标：</strong>
          {{ selectedLocation.lng.toFixed(6) }}, {{ selectedLocation.lat.toFixed(6) }}
        </p>
      </div>
    </div>
    
    <!-- 加载提示 -->
    <div v-if="loading" class="loading-overlay">
      <el-icon class="is-loading" size="24"><Loading /></el-icon>
      <p>正在加载地图...</p>
    </div>
    
    <!-- 错误提示 -->
    <div v-if="error" class="error-overlay">
      <el-icon color="#F56C6C" size="24"><Warning /></el-icon>
      <p>{{ error }}</p>
      <el-button size="small" @click="retryLoad">重试</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Check, Delete, Location, Loading, Warning } from '@element-plus/icons-vue'
import { mapService } from '@/services/mapService'

// 定义props
interface Props {
  initialLocation?: {
    lng: number
    lat: number
    address?: string
  }
  apiKey?: string
}

const props = withDefaults(defineProps<Props>(), {
  initialLocation: undefined,
  apiKey: undefined
})

// 定义事件
const emit = defineEmits<{
  locationSelected: [location: { lng: number; lat: number; address: string }]
  locationCleared: []
}>()

// 响应式数据
const mapContainer = ref<HTMLElement>()
const map = ref<any>(null)
const marker = ref<any>(null)
const selectedLocation = ref<{ lng: number; lat: number; address: string } | null>(null)
const searchKeyword = ref('')
const searching = ref(false)
const gettingLocation = ref(false)
const loading = ref(true)
const error = ref('')

// 百度地图API密钥
const BAIDU_API_KEY = props.apiKey || import.meta.env.VITE_BAIDU_API_KEY || 'MqycZpQuzhz8JuMH40W3yfVImAYcJ1g9'

// 初始化地图
const initMap = async () => {
  if (!mapContainer.value) return
  
  try {
    loading.value = true
    error.value = ''
    
    // 动态加载百度地图API
    const BMap = await loadBaiduMapAPI()
    
    // 创建地图实例
    map.value = new BMap.Map(mapContainer.value)
    
    // 设置地图中心点和缩放级别
    const point = new BMap.Point(116.397428, 39.90923) // 北京
    map.value.centerAndZoom(point, 11)
    
    // 启用滚轮缩放
    map.value.enableScrollWheelZoom(true)
    
    // 添加地图控件
    map.value.addControl(new BMap.NavigationControl())
    map.value.addControl(new BMap.ScaleControl())
    map.value.addControl(new BMap.OverviewMapControl())
    map.value.addControl(new BMap.MapTypeControl())
    
    // 添加地图点击事件
    map.value.addEventListener('click', handleMapClick)
    
    // 设置初始位置
    if (props.initialLocation) {
      setLocation(props.initialLocation.lng, props.initialLocation.lat, props.initialLocation.address || '')
    }
    
    loading.value = false
  } catch (err) {
    console.error('地图初始化失败:', err)
    error.value = '地图加载失败，请检查网络连接或API密钥'
    loading.value = false
  }
}

// 动态加载百度地图API
const loadBaiduMapAPI = (): Promise<any> => {
  return new Promise((resolve, reject) => {
    // 检查是否已经加载
    if (window.BMap) {
      resolve(window.BMap)
      return
    }
    
    // 设置全局回调函数
    window.initBaiduMap = () => {
      if (window.BMap) {
        resolve(window.BMap)
      } else {
        reject(new Error('百度地图API加载失败'))
      }
    }
    
    // 创建script标签
    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.async = true
    script.src = `https://api.map.baidu.com/api?v=3.0&ak=${BAIDU_API_KEY}&callback=initBaiduMap`
    
    script.onerror = () => {
      reject(new Error('百度地图API加载失败'))
    }
    
    // 设置超时处理
    const timeout = setTimeout(() => {
      reject(new Error('百度地图API加载超时'))
    }, 10000)
    
    // 监听加载完成
    const checkLoad = () => {
      if (window.BMap) {
        clearTimeout(timeout)
        resolve(window.BMap)
      } else {
        setTimeout(checkLoad, 100)
      }
    }
    
    document.head.appendChild(script)
    checkLoad()
  })
}

// 处理地图点击
const handleMapClick = async (e: any) => {
  const lng = e.point.lng
  const lat = e.point.lat
  
  try {
    // 获取地址信息
    const address = await getAddressFromCoordinates(lng, lat)
    setLocation(lng, lat, address || '未知地址')
  } catch (err) {
    console.error('获取地址失败:', err)
    setLocation(lng, lat, '未知地址')
  }
}

// 设置位置
const setLocation = (lng: number, lat: number, address: string) => {
  selectedLocation.value = { lng, lat, address }
  
  // 更新地图中心
  if (map.value) {
    const point = new window.BMap.Point(lng, lat)
    map.value.centerAndZoom(point, 15)
  }
  
  // 更新标记
  updateMarker(lng, lat)
}

// 暴露方法给父组件调用
defineExpose({
  setLocation
})

// 更新地图标记
const updateMarker = (lng: number, lat: number) => {
  if (!map.value) return
  
  // 移除旧标记
  if (marker.value) {
    map.value.removeOverlay(marker.value)
  }
  
  // 创建新标记
  const point = new window.BMap.Point(lng, lat)
  marker.value = new window.BMap.Marker(point, {
    icon: new window.BMap.Icon(
      'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzIiIGhlaWdodD0iMzIiIHZpZXdCb3g9IjAgMCAzMiAzMiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE2IDJDNy4xNjMgMiAwIDkuMTYzIDAgMTdjMCA5LjUgMTYgMTMgMTYgMTNzMTYtMy41IDE2LTEzQzMyIDkuMTYzIDI0LjgzNyAyIDE2IDJaIiBmaWxsPSIjRkY0NDQ0Ii8+CjxjaXJjbGUgY3g9IjE2IiBjeT0iMTYiIHI9IjYiIGZpbGw9IndoaXRlIi8+Cjwvc3ZnPgo=',
      new window.BMap.Size(32, 32)
    )
  })
  
  map.value.addOverlay(marker.value)
}

// 处理搜索
const handleSearch = async () => {
  if (!searchKeyword.value.trim()) {
    ElMessage.warning('请输入搜索关键词')
    return
  }
  
  searching.value = true
  try {
    console.log('开始搜索地址:', searchKeyword.value)
    
    // 使用地图服务搜索地址
    const location = await mapService.searchAddress(searchKeyword.value)
    
    if (location) {
      console.log('搜索成功，设置位置:', location)
      setLocation(location.lng, location.lat, location.address)
      ElMessage.success(`已找到位置: ${location.address}`)
    } else {
      ElMessage.warning('未找到该地址，请尝试其他关键词')
    }
    
  } catch (error) {
    console.error('搜索失败:', error)
    ElMessage.error('搜索失败，请重试')
  } finally {
    searching.value = false
  }
}

// 获取当前位置
const getCurrentLocation = () => {
  gettingLocation.value = true
  
  if (!navigator.geolocation) {
    ElMessage.error('浏览器不支持地理定位')
    gettingLocation.value = false
    return
  }
  
  navigator.geolocation.getCurrentPosition(
    async (position) => {
      const lat = position.coords.latitude
      const lng = position.coords.longitude
      
      try {
        const address = await getAddressFromCoordinates(lng, lat)
        setLocation(lng, lat, address || '当前位置')
        ElMessage.success('已定位到当前位置')
      } catch (err) {
        setLocation(lng, lat, '当前位置')
        ElMessage.success('已定位到当前位置')
      }
      gettingLocation.value = false
    },
    (error) => {
      console.error('获取位置失败:', error)
      ElMessage.error('获取位置失败，请检查定位权限')
      gettingLocation.value = false
    }
  )
}

// 根据坐标获取地址
const getAddressFromCoordinates = async (lng: number, lat: number): Promise<string> => {
  // 使用地图服务进行逆地理编码
  return await mapService.getAddressFromCoordinates(lng, lat) || '未知地址'
}

// 确认位置
const confirmLocation = () => {
  if (selectedLocation.value) {
    emit('locationSelected', selectedLocation.value)
    ElMessage.success('位置已确认')
  }
}

// 清除位置
const clearLocation = () => {
  selectedLocation.value = null
  searchKeyword.value = ''
  
  if (marker.value && map.value) {
    map.value.removeOverlay(marker.value)
    marker.value = null
  }
  
  emit('locationCleared')
  ElMessage.info('位置已清除')
}

// 重试加载
const retryLoad = () => {
  initMap()
}

// 监听初始位置变化
watch(() => props.initialLocation, (newLocation) => {
  if (newLocation && map.value) {
    setLocation(newLocation.lng, newLocation.lat, newLocation.address || '')
  }
}, { deep: true })

// 组件挂载时初始化地图
onMounted(() => {
  nextTick(() => {
    initMap()
  })
})

// 组件卸载时清理资源
onUnmounted(() => {
  if (map.value) {
    // 百度地图不需要显式销毁
    map.value = null
  }
})

// 声明全局类型
declare global {
  interface Window {
    BMap: any
    initBaiduMap: () => void
  }
}
</script>

<style scoped>
.baidu-map-container {
  width: 100%;
  height: 100%;
  position: relative;
  display: flex;
  flex-direction: column;
}

.map-container {
  width: 100%;
  height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  position: relative;
}

.search-bar {
  margin-bottom: 12px;
}

.search-input {
  width: 100%;
}

.map-controls {
  margin-top: 12px;
  display: flex;
  gap: 8px;
  justify-content: center;
  flex-wrap: wrap;
}

.location-info {
  margin-top: 12px;
  padding: 12px;
  background: #f0f9ff;
  border-radius: 4px;
  border: 1px solid #b3d8ff;
}

.location-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 600;
  color: #409EFF;
}

.location-details p {
  margin: 4px 0;
  font-size: 14px;
  color: #606266;
}

.location-details .address {
  color: #303133;
  font-weight: 500;
}

.location-details .coordinates {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #909399;
}

.loading-overlay,
.error-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-overlay p,
.error-overlay p {
  margin: 8px 0;
  color: #606266;
  text-align: center;
}

.error-overlay {
  background: rgba(255, 255, 255, 0.95);
}

.error-overlay p {
  color: #F56C6C;
  margin-bottom: 12px;
}

@media (max-width: 768px) {
  .map-controls {
    flex-direction: column;
  }
  
  .map-controls .el-button {
    width: 100%;
  }
}
</style> 