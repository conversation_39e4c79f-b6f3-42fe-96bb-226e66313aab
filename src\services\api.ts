// 导入API配置
import { getApiBaseUrl } from './apiConfig'

// 通用响应类型 - 适配后端返回格式
interface ApiResponse<T = any> {
  code: number
  message: string
  token?: string | null
  refreshToken?: string | null
  data: T
}

// 分页响应类型 - 适配后端返回格式（camelCase）
interface PagedResponse<T> {
  pageData: T[]
  totalCount: number
  pageCount: number
}

// 银行账户类型
export interface BankAccount {
  id: number
  bankName: string
  accountNumber: string
  accountName: string
  branchName?: string
  isDefault: boolean
  supplierId?: number
  customerId?: number
}

// 供应商银行账户类型
export interface SupplierBankAccount {
  id: number
  bankName: string
  accountNumber: string
  accountName: string
  branchName?: string
  isDefault: boolean
  supplierId: number
}

// 客户银行账户类型
export interface CustomerBankAccount {
  id: number
  bankName: string
  accountNumber: string
  accountName: string
  branchName?: string
  accountType?: string
  isDefault: boolean
  remarks?: string
  customerId: number
}

// 联系信息类型
export interface Contact {
  id: number
  contactName: string
  contactPhone?: string
  contactEmail?: string
  contactAddress?: string
  position?: string
  isPrimary: boolean
  supplierId?: number
  customerId?: number
}

// 供应商联系信息类型
export interface SupplierContact {
  id: number
  contactName: string
  contactPhone?: string
  contactEmail?: string
  contactAddress?: string
  position?: string
  isPrimary: boolean
  supplierId: number
}

// 客户联系信息类型
export interface CustomerContact {
  id: number
  contactName: string
  contactPhone?: string
  contactEmail?: string
  contactAddress?: string
  position?: string
  isPrimary: boolean
  remarks?: string
  customerId: number
}

// 服务人员类型
export interface ServicePerson {
  id: number
  affiliatedMarket?: string
  salespersonName?: string
  salespersonId?: number
  supplierId?: number
  customerId?: number
}

// 供应商服务人员类型
export interface SupplierServicePerson {
  id: number
  affiliatedMarket?: string
  salespersonName?: string
  salespersonId?: number
  supplierId: number
}

// 客户服务人员类型
export interface CustomerServicePerson {
  id: number
  affiliatedMarket?: string
  salespersonName?: string
  salespersonId?: number
  serviceStartTime?: string
  serviceEndTime?: string
  serviceStatus?: string
  remarks?: string
  customerId: number
}

// 供应商相关类型
export interface Supplier {
  id: number
  supplierNumber: string
  supplierName: string
  supplierType: string
  personInChargeName?: string
  personInChargePhone?: string
  idCard?: string
  location?: string
  industry?: string
  unifiedSocialCreditCode?: string
  mnemonicCode?: string
  usageStatus: boolean
  entryUnit?: string
  usageUnit?: string
  remarks?: string
  isArchived: boolean
  longitude?: number
  latitude?: number
  createTime: string
  updateTime: string
  // 关联数据
  bankAccounts?: SupplierBankAccount[]
  contacts?: SupplierContact[]
  servicePersons?: SupplierServicePerson[]
  productPrices?: any[]
  attachments?: any[]
  associatedAccount?: any
}

// 客户相关类型
export interface Customer {
  id: number
  customerNumber: string
  customerName: string
  customerType: string
  personInChargeName?: string
  personInChargePhone?: string
  idCard?: string
  location?: string
  industry?: string
  unifiedSocialCreditCode?: string
  mnemonicCode?: string
  customerStage?: string
  customerLevel?: string
  customerGroupClassification?: string
  usageStatus: string
  entryUnit?: string
  usageUnit?: string
  superiorCustomerId?: number
  superiorCustomer?: Customer
  customTags?: string
  remarks?: string
  isArchived: boolean
  longitude?: number
  latitude?: number
  createTime: string
  updateTime: string
  // 关联数据
  bankAccounts?: CustomerBankAccount[]
  contacts?: CustomerContact[]
  servicePersons?: CustomerServicePerson[]
  productSolutions?: any[]
  discountSolutions?: any[]
  attachments?: any[]
  associatedAccount?: any
}

// 查询参数类型
export interface SupplierQueryParams {
  supplierNumber?: string
  supplierName?: string
  supplierType?: string
  usageStatus?: boolean | null
  pageIndex: number
  pageSize: number
}

export interface CustomerQueryParams {
  customerNumber?: string
  customerName?: string
  customerType?: string
  customerLevel?: string
  usageStatus?: string
  pageIndex: number
  pageSize: number
}

// 添加供应商参数
export interface AddSupplierParams {
  supplierNumber: string
  supplierName: string
  supplierType: string
  personInChargeName?: string
  personInChargePhone?: string
  idCard?: string
  location?: string
  industry?: string
  unifiedSocialCreditCode?: string
  mnemonicCode?: string
  usageStatus: boolean
  entryUnit?: string
  usageUnit?: string
  remarks?: string
  isArchived: boolean
  longitude?: number | null
  latitude?: number | null
}

// 添加客户参数
export interface AddCustomerParams {
  customerNumber: string
  customerName: string
  customerType: string
  personInChargeName?: string
  personInChargePhone?: string
  idCard?: string
  location?: string
  industry?: string
  unifiedSocialCreditCode?: string
  mnemonicCode?: string
  customerStage?: string
  customerLevel?: string
  customerGroupClassification?: string
  usageStatus: string
  entryUnit?: string
  usageUnit?: string
  superiorCustomerId?: number | null
  customTags?: string
  remarks?: string
  isArchived: boolean
  longitude?: number | null
  latitude?: number | null
}

// 通用HTTP请求方法
async function request<T>(url: string, options: RequestInit = {}): Promise<ApiResponse<T>> {
  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      ...options.headers,
    },
  }

  // 根据请求方法获取对应的API基础URL
  const method = options.method || 'GET'
  const API_BASE_URL = getApiBaseUrl(method)
  
  const fullUrl = `${API_BASE_URL}${url}`
  console.log(`发送${method}请求到: ${fullUrl}`)

  const response = await fetch(fullUrl, {
    ...defaultOptions,
    ...options,
  })

  if (!response.ok) {
    const errorText = await response.text();
    console.error(`API请求失败 - URL: ${fullUrl}, 状态: ${response.status}, 响应: ${errorText}`);
    throw new Error(`HTTP error! status: ${response.status}, response: ${errorText}`)
  }

  const result = await response.json()

  // 适配后端返回的数据结构 - 后端配置了camelCase命名策略
  return {
    code: result.code,
    message: result.message,
    token: result.token,
    refreshToken: result.refreshToken,
    data: result.data
  }
}

// 供应商API
export const supplierApi = {
  // 获取供应商列表
  getList: (params: SupplierQueryParams): Promise<ApiResponse<PagedResponse<Supplier>>> => {
    const searchParams = new URLSearchParams()
    Object.entries(params).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        // 将前端的小写参数名转换为后端期望的大写参数名
        const backendKey = key === 'supplierNumber' ? 'SupplierNumber' :
                          key === 'supplierName' ? 'SupplierName' :
                          key === 'supplierType' ? 'SupplierType' :
                          key === 'usageStatus' ? 'UsageStatus' :
                          key === 'pageIndex' ? 'PageIndex' :
                          key === 'pageSize' ? 'PageSize' : key
        searchParams.append(backendKey, String(value))
      }
    })
    return request(`/api/Management/GetSupplier?${searchParams.toString()}`)
  },

  // 获取供应商详情
  getDetail: (id: number): Promise<ApiResponse<Supplier>> => {
    return request(`/api/Management/GetSupplierDetail?id=${id}`)
  },

  // 添加供应商
  add: (data: AddSupplierParams): Promise<ApiResponse<Supplier>> => {
    // 将前端的小写参数名转换为后端期望的大写参数名
    const backendData: any = {}
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        const backendKey = key === 'supplierNumber' ? 'SupplierNumber' :
                          key === 'supplierName' ? 'SupplierName' :
                          key === 'supplierType' ? 'SupplierType' :
                          key === 'personInChargeName' ? 'PersonInChargeName' :
                          key === 'personInChargePhone' ? 'PersonInChargePhone' :
                          key === 'idCard' ? 'IdCard' :
                          key === 'location' ? 'Location' :
                          key === 'industry' ? 'Industry' :
                          key === 'unifiedSocialCreditCode' ? 'UnifiedSocialCreditCode' :
                          key === 'mnemonicCode' ? 'MnemonicCode' :
                          key === 'usageStatus' ? 'UsageStatus' :
                          key === 'entryUnit' ? 'EntryUnit' :
                          key === 'usageUnit' ? 'UsageUnit' :
                          key === 'remarks' ? 'Remarks' :
                          key === 'isArchived' ? 'IsArchived' :
                          key === 'longitude' ? 'Longitude' :
                          key === 'latitude' ? 'Latitude' : key
        backendData[backendKey] = value
      }
    })
    
    return request('/api/Management/AddSupplier', {
      method: 'POST',
      body: JSON.stringify(backendData),
    })
  },
}

// 客户API
export const customerApi = {
  // 获取客户列表
  getList: (params: CustomerQueryParams): Promise<ApiResponse<PagedResponse<Customer>>> => {
    const searchParams = new URLSearchParams()
    
    Object.entries(params).forEach(([key, value]) => {
      // 只传递非空值
      if (value !== undefined && value !== null && value !== '') {
        // 将前端的小写参数名转换为后端期望的大写参数名
        const backendKey = key === 'customerNumber' ? 'CustomerNumber' :
                          key === 'customerName' ? 'CustomerName' :
                          key === 'customerType' ? 'CustomerType' :
                          key === 'customerLevel' ? 'CustomerLevel' :
                          key === 'usageStatus' ? 'UsageStatus' :
                          key === 'pageIndex' ? 'PageIndex' :
                          key === 'pageSize' ? 'PageSize' : key
        searchParams.append(backendKey, String(value))
      }
    })
    
    const url = `/api/Management/GetCustomer?${searchParams.toString()}`
    return request(url)
  },

  // 获取客户详情
  getDetail: (id: number): Promise<ApiResponse<Customer>> => {
    return request(`/api/Management/GetCustomerDetail?id=${id}`)
  },

  // 添加客户
  add: (data: AddCustomerParams): Promise<ApiResponse<Customer>> => {
    // 将前端的小写参数名转换为后端期望的大写参数名
    const backendData: any = {}
    Object.entries(data).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        const backendKey = key === 'customerNumber' ? 'CustomerNumber' :
                          key === 'customerName' ? 'CustomerName' :
                          key === 'customerType' ? 'CustomerType' :
                          key === 'personInChargeName' ? 'PersonInChargeName' :
                          key === 'personInChargePhone' ? 'PersonInChargePhone' :
                          key === 'idCard' ? 'IdCard' :
                          key === 'location' ? 'Location' :
                          key === 'industry' ? 'Industry' :
                          key === 'unifiedSocialCreditCode' ? 'UnifiedSocialCreditCode' :
                          key === 'mnemonicCode' ? 'MnemonicCode' :
                          key === 'customerStage' ? 'CustomerStage' :
                          key === 'customerLevel' ? 'CustomerLevel' :
                          key === 'customerGroupClassification' ? 'CustomerGroupClassification' :
                          key === 'usageStatus' ? 'UsageStatus' :
                          key === 'entryUnit' ? 'EntryUnit' :
                          key === 'usageUnit' ? 'UsageUnit' :
                          key === 'superiorCustomerId' ? 'SuperiorCustomerId' :
                          key === 'customTags' ? 'CustomTags' :
                          key === 'remarks' ? 'Remarks' :
                          key === 'isArchived' ? 'IsArchived' :
                          key === 'longitude' ? 'Longitude' :
                          key === 'latitude' ? 'Latitude' : key
        backendData[backendKey] = value
      }
    })
    
    return request('/api/Management/AddCustomer', {
      method: 'POST',
      body: JSON.stringify(backendData),
    })
  },
}

// 导出默认API对象
export default {
  supplier: supplierApi,
  customer: customerApi,
} 