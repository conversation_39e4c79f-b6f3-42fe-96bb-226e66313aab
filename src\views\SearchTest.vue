<template>
  <div class="search-test">
    <el-card class="test-card">
      <template #header>
        <div class="card-header">
          <h2>地图搜索功能测试</h2>
          <el-tag :type="mapService.hasValidApiKey() ? 'success' : 'warning'">
            {{ mapService.hasValidApiKey() ? '百度地图API模式' : '模拟模式' }}
          </el-tag>
        </div>
      </template>
      
      <div class="test-content">
        <el-row :gutter="20">
          <el-col :span="12">
            <div class="test-section">
              <h3>地址搜索测试</h3>
              <el-form :model="searchForm" label-width="100px">
                <el-form-item label="搜索地址">
                  <el-input 
                    v-model="searchForm.address" 
                    placeholder="请输入地址，如：清华大学、天安门、王府井等"
                    @keyup.enter="testSearch"
                  />
                </el-form-item>
                <el-form-item>
                  <el-button type="primary" @click="testSearch" :loading="searching">
                    搜索测试
                  </el-button>
                  <el-button @click="clearResults">清除结果</el-button>
                </el-form-item>
              </el-form>
              
              <div v-if="searchResults.length > 0" class="results-display">
                <h4>搜索结果：</h4>
                <el-table :data="searchResults" style="width: 100%">
                  <el-table-column prop="address" label="地址" />
                  <el-table-column prop="lng" label="经度" width="120">
                    <template #default="scope">
                      {{ scope.row.lng.toFixed(6) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="lat" label="纬度" width="120">
                    <template #default="scope">
                      {{ scope.row.lat.toFixed(6) }}
                    </template>
                  </el-table-column>
                  <el-table-column prop="mode" label="模式" width="80">
                    <template #default="scope">
                      <el-tag :type="scope.row.mode === 'real' ? 'success' : 'warning'" size="small">
                        {{ scope.row.mode === 'real' ? '真实' : '模拟' }}
                      </el-tag>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-col>
          
          <el-col :span="12">
            <div class="test-section">
              <h3>快速测试</h3>
              <p>点击下面的按钮快速测试常见地址：</p>
              
              <div class="quick-test-buttons">
                <el-button 
                  v-for="testAddress in testAddresses" 
                  :key="testAddress"
                  @click="quickTest(testAddress)"
                  size="small"
                  style="margin: 4px;"
                >
                  {{ testAddress }}
                </el-button>
              </div>
              
              <div class="api-status">
                <h4>API状态信息：</h4>
                <p><strong>API密钥状态：</strong>
                  <el-tag :type="mapService.hasValidApiKey() ? 'success' : 'warning'">
                    {{ mapService.hasValidApiKey() ? '已配置' : '未配置' }}
                  </el-tag>
                </p>
                <p><strong>当前模式：</strong>
                  <el-tag :type="mapService.hasValidApiKey() ? 'success' : 'warning'">
                    {{ mapService.hasValidApiKey() ? '真实API' : '模拟模式' }}
                  </el-tag>
                </p>
                <p><strong>API密钥：</strong>
                  <code>{{ getApiKeyDisplay() }}</code>
                </p>
              </div>
            </div>
          </el-col>
        </el-row>
        
        <el-divider />
        
        <div class="test-section">
          <h3>地图定位对话框测试</h3>
          <el-button type="primary" @click="showMapDialog = true">
            打开地图定位对话框
          </el-button>
          
          <div v-if="dialogResult" class="result-display">
            <h4>对话框选择结果：</h4>
            <p><strong>地址：</strong>{{ dialogResult.address }}</p>
            <p><strong>经度：</strong>{{ dialogResult.lng.toFixed(6) }}</p>
            <p><strong>纬度：</strong>{{ dialogResult.lat.toFixed(6) }}</p>
          </div>
        </div>
      </div>
    </el-card>
    
    <!-- 地图定位对话框 -->
    <MapLocationDialog
      v-model="showMapDialog"
      :initial-address="searchForm.address"
      @location-selected="handleDialogLocationSelected"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { mapService } from '@/services/mapService'
import MapLocationDialog from '@/components/MapLocationDialog.vue'

// 搜索表单
const searchForm = reactive({
  address: '清华大学'
})

// 状态变量
const searching = ref(false)
const showMapDialog = ref(false)
const searchResults = ref<Array<{
  address: string
  lng: number
  lat: number
  mode: 'real' | 'mock'
  timestamp: string
}>>([])
const dialogResult = ref<{ lng: number; lat: number; address: string } | null>(null)

// 测试地址列表
const testAddresses = [
  '清华大学',
  '北京大学',
  '天安门',
  '故宫',
  '王府井',
  '西单',
  '朝阳区',
  '海淀区',
  '上海外滩',
  '广州塔',
  '深圳湾',
  '杭州西湖'
]

// 测试搜索
const testSearch = async () => {
  if (!searchForm.address.trim()) {
    ElMessage.warning('请输入搜索地址')
    return
  }
  
  searching.value = true
  try {
    console.log('开始测试搜索:', searchForm.address)
    
    const startTime = Date.now()
    const location = await mapService.searchAddress(searchForm.address)
    const endTime = Date.now()
    
    if (location) {
      const result = {
        address: location.address,
        lng: location.lng,
        lat: location.lat,
        mode: mapService.hasValidApiKey() ? 'real' : 'mock',
        timestamp: new Date().toLocaleTimeString()
      }
      
      searchResults.value.unshift(result)
      
      ElMessage.success(`搜索成功 (${endTime - startTime}ms): ${location.address}`)
    } else {
      ElMessage.warning('未找到该地址')
    }
  } catch (error) {
    console.error('搜索测试失败:', error)
    ElMessage.error('搜索测试失败')
  } finally {
    searching.value = false
  }
}

// 快速测试
const quickTest = async (address: string) => {
  searchForm.address = address
  await testSearch()
}

// 清除结果
const clearResults = () => {
  searchResults.value = []
  dialogResult.value = null
  ElMessage.info('结果已清除')
}

// 处理对话框位置选择
const handleDialogLocationSelected = (location: { lng: number; lat: number; address: string }) => {
  dialogResult.value = location
  ElMessage.success('对话框位置选择成功')
}

// 获取API密钥显示
const getApiKeyDisplay = () => {
  const key = import.meta.env.VITE_BAIDU_API_KEY
  if (!key || key === 'your_baidu_api_key') {
    return '未配置'
  }
  return key.length > 10 ? `${key.substring(0, 10)}...` : key
}
</script>

<style scoped>
.search-test {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.test-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h2 {
  margin: 0;
  color: #303133;
}

.test-content {
  padding: 20px 0;
}

.test-section {
  margin-bottom: 30px;
}

.test-section h3 {
  color: #409EFF;
  margin-bottom: 15px;
  border-bottom: 2px solid #409EFF;
  padding-bottom: 8px;
}

.test-section h4 {
  color: #606266;
  margin: 15px 0 10px 0;
}

.results-display {
  margin-top: 20px;
  padding: 15px;
  background: #f0f9ff;
  border-radius: 8px;
  border: 1px solid #b3d8ff;
}

.results-display h4 {
  margin: 0 0 10px 0;
  color: #409EFF;
}

.results-display p {
  margin: 5px 0;
  color: #606266;
}

.results-display strong {
  color: #303133;
}

.quick-test-buttons {
  margin: 15px 0;
}

.api-status {
  margin-top: 20px;
  padding: 15px;
  background: #f5f7fa;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
}

.api-status p {
  margin: 8px 0;
  color: #606266;
}

.api-status strong {
  color: #303133;
}

code {
  background: #f0f0f0;
  padding: 2px 6px;
  border-radius: 3px;
  font-family: 'Courier New', monospace;
  font-size: 12px;
}
</style> 