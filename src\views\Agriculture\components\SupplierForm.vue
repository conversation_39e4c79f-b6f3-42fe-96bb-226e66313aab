<template>
  <div class="supplier-form">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="140px"
      @submit.prevent="handleSubmit"
    >
      <!-- 供应商类型 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="供应商类型" prop="supplierType" required>
            <el-radio-group v-model="formData.supplierType">
              <el-radio label="个人">个人</el-radio>
              <el-radio label="企业">企业</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 负责人信息 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="负责人手机号" prop="personInChargePhone" required>
            <el-input v-model="formData.personInChargePhone" placeholder="请输入负责人手机号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="负责人姓名" prop="personInChargeName" required>
            <el-input v-model="formData.personInChargeName" placeholder="请输入负责人姓名" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 供应商名称 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="供应商名称" prop="supplierName" required>
            <el-input v-model="formData.supplierName" placeholder="请输入供应商名称" />
            <div class="form-tip">企业供应商请输入全称，个人供应商建议输入省市县，姓名。</div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 身份证和所在地 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="身份证" prop="idCard">
            <el-input v-model="formData.idCard" placeholder="请填写身份证号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所在地" prop="location">
            <div class="location-input-group">
              <el-select v-model="formData.locationProvince" placeholder="请选择" style="width: 120px">
                <el-option label="北京市" value="北京市" />
                <el-option label="上海市" value="上海市" />
                <el-option label="广东省" value="广东省" />
                <el-option label="河南省" value="河南省" />
                <el-option label="江苏省" value="江苏省" />
                <el-option label="浙江省" value="浙江省" />
                <el-option label="山东省" value="山东省" />
                <el-option label="四川省" value="四川省" />
                <el-option label="湖北省" value="湖北省" />
                <el-option label="湖南省" value="湖南省" />
              </el-select>
              <el-input 
                v-model="formData.locationDetail" 
                :placeholder="formData.longitude && formData.latitude ? '已定位位置' : '必须填写四个字以上地址'" 
                style="flex: 1; margin-left: 8px;"
                :class="{ 'location-selected': formData.longitude && formData.latitude }"
              />
              <el-button type="primary" @click="handleMapLocation" style="margin-left: 8px;">
                <el-icon><Location /></el-icon>
                地图定位
              </el-button>
            </div>
            

          </el-form-item>
        </el-col>
      </el-row>

      <!-- 地图定位对话框 -->
      <MapLocationDialog
        v-model="showMap"
        :initial-address="formData.locationDetail"
        :initial-province="formData.locationProvince"
        @location-selected="handleLocationSelected"
      />

      <!-- 行业和统一社会信用代码 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所属行业" prop="industry">
            <el-input v-model="formData.industry" placeholder="请输入所属行业" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="统一社会信用代码" prop="unifiedSocialCreditCode">
            <el-input v-model="formData.unifiedSocialCreditCode" placeholder="请填写统一社会信用代码(税号)" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 助记码和使用状态 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="助记码" prop="mnemonicCode">
            <el-input v-model="formData.mnemonicCode" placeholder="请输入助记码" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="使用状态" prop="usageStatus" required>
            <el-radio-group v-model="formData.usageStatus">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 录入单位和使用单位 -->
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="录入单位" prop="entryUnit">
            <el-input v-model="formData.entryUnit" placeholder="请输入录入单位" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="使用单位" prop="usageUnit">
            <el-input v-model="formData.usageUnit" placeholder="请输入使用单位" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 备注 -->
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="备注" prop="remarks">
            <el-input
              v-model="formData.remarks"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-col>
      </el-row>



      <!-- 表单操作按钮 -->
      <el-form-item class="form-actions">
        <el-button type="primary" @click="handleSubmit" :loading="submitting" size="large">
          <el-icon><Check /></el-icon>
          提交保存
        </el-button>
        <el-button @click="handleReset" size="large">
          <el-icon><Refresh /></el-icon>
          重置表单
        </el-button>
        <el-button @click="handleCancel" size="large">
          <el-icon><Close /></el-icon>
          取消
        </el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { Check, Refresh, Close, Location, Delete } from '@element-plus/icons-vue'
import { supplierApi, type AddSupplierParams } from '@/services/api'
import { mapService } from '@/services/mapService'
import MapLocationDialog from '@/components/MapLocationDialog.vue'

// 定义事件
const emit = defineEmits<{
  submitSuccess: []
  cancel: []
}>()

// 表单引用
const formRef = ref<FormInstance>()
const submitting = ref(false)
const showMap = ref(false)
const initialMapLocation = ref<{ lng: number; lat: number; address?: string } | null>(null)

// 表单数据
const formData = reactive({
  supplierNumber: '',
  supplierName: '',
  supplierType: '个人',
  personInChargeName: '',
  personInChargePhone: '',
  idCard: '',
  locationProvince: '',
  locationDetail: '',
  industry: '',
  unifiedSocialCreditCode: '',
  mnemonicCode: '',
  usageStatus: true,
  entryUnit: '',
  usageUnit: '',
  remarks: '',
  isArchived: false,
  longitude: null as number | null,
  latitude: null as number | null,
  location: ''
})

// 表单验证规则
const rules: FormRules = {
  // supplierName: [
  //   { required: true, message: '请输入供应商名称', trigger: 'blur' },
  //   { min: 2, max: 100, message: '长度在 2 到 100 个字符', trigger: 'blur' }
  // ],
  // supplierType: [
  //   { required: true, message: '请选择供应商类型', trigger: 'change' }
  // ],
  // personInChargeName: [
  //   { required: true, message: '请输入负责人姓名', trigger: 'blur' }
  // ],
  // personInChargePhone: [
  //   { required: true, message: '请输入负责人手机号', trigger: 'blur' },
  //   { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  // ],
  // usageStatus: [
  //   { required: true, message: '请选择使用状态', trigger: 'change' }
  // ],
  // idCard: [
  //   { pattern: /(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/, message: '请输入正确的身份证号', trigger: 'blur' }
  // ],
  // unifiedSocialCreditCode: [
  //   { pattern: /^[0-9A-HJ-NPQRTUWXY]{2}\d{6}[0-9A-HJ-NPQRTUWXY]{10}$/, message: '请输入正确的统一社会信用代码', trigger: 'blur' }
  // ]
}



// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    // 合并地址信息
    formData.location = `${formData.locationProvince} ${formData.locationDetail}`.trim()
    
    // 验证地址信息
    if (!formData.location.trim()) {
      ElMessage.warning('请填写地址信息')
      submitting.value = false
      return
    }
    
    // 如果没有经纬度，尝试自动获取
    if (!formData.longitude || !formData.latitude) {
      ElMessage.info('正在获取地址坐标...')
      const location = await mapService.searchAddress(formData.location)
      if (location) {
        formData.longitude = location.lng
        formData.latitude = location.lat
        ElMessage.success('已自动获取地址坐标')
      } else {
        ElMessage.warning('无法获取地址坐标，将使用默认值')
        formData.longitude = 116.397428
        formData.latitude = 39.90923
      }
    }
    
    // 生成供应商编号（简单的时间戳生成）
    formData.supplierNumber = `SP${Date.now()}`
    
    // 调用API添加供应商
    const result = await supplierApi.add(formData as AddSupplierParams)
    
    if (result.code === 200) {
      ElMessage.success('供应商添加成功')
      emit('submitSuccess')
      handleReset()
    } else {
      ElMessage.error(result.message || '添加失败')
    }
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('提交失败，请检查表单信息')
  } finally {
    submitting.value = false
  }
}

// 重置表单
const handleReset = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  Object.assign(formData, {
    supplierNumber: '',
    supplierName: '',
    supplierType: '个人',
    personInChargeName: '',
    personInChargePhone: '',
    idCard: '',
    locationProvince: '',
    locationDetail: '',
    industry: '',
    unifiedSocialCreditCode: '',
    mnemonicCode: '',
    usageStatus: true,
    entryUnit: '',
    usageUnit: '',
    remarks: '',
    isArchived: false,
      longitude: null as number | null,
  latitude: null as number | null,
    location: ''
  })
  showMap.value = false
  initialMapLocation.value = null
  ElMessage.info('表单已重置')
}

// 取消
const handleCancel = () => {
  emit('cancel')
}

// 处理位置选择
const handleLocationSelected = (location: { lng: number; lat: number; address: string }) => {
  formData.longitude = location.lng
  formData.latitude = location.lat
  
  // 解析地址信息，更新表单
  const addressParts = location.address.split(' ')
  if (addressParts.length >= 2) {
    formData.locationProvince = addressParts[0]
    formData.locationDetail = addressParts.slice(1).join(' ')
  } else {
    formData.locationDetail = location.address
  }
  
  showMap.value = false
  ElMessage.success('位置已设置')
}

// 处理位置清除
const handleLocationCleared = () => {
  formData.longitude = null
  formData.latitude = null
  formData.locationDetail = ''
  ElMessage.info('位置已清除')
}

// 清除位置
const clearLocation = () => {
  formData.longitude = null
  formData.latitude = null
  formData.location = ''
  formData.locationDetail = ''
  ElMessage.info('位置已清除')
}

// 处理地图定位
const handleMapLocation = () => {
  // 显示地图定位对话框
  showMap.value = true
}
</script>

<style scoped>
.supplier-form {
  padding: 20px 0;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

:deep(.el-input-number) {
  width: 100%;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.4;
}

.location-input-group {
  display: flex;
  align-items: center;
}

.location-selected {
  border-color: #67C23A !important;
  background-color: #f0f9ff !important;
}

.location-selected:focus {
  border-color: #67C23A !important;
  box-shadow: 0 0 0 2px rgba(103, 194, 58, 0.2) !important;
}

.map-container {
  position: relative;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  overflow: hidden;
  height: 500px;
}

.map-close {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reference-unit-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.unit-count {
  font-size: 12px;
  color: #909399;
  white-space: nowrap;
}

.form-actions {
  margin-top: 40px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
  text-align: center;
}

.form-actions .el-button {
  margin: 0 10px;
  min-width: 120px;
}

/* 对话框样式 */
:deep(.supplier-dialog .el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}
</style> 